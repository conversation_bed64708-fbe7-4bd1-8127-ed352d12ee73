import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import {
  Card, Button, Row, Col, Badge, <PERSON><PERSON>, Spinner, Form
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft, faEdit, faDownload, faFileInvoiceDollar,
  faUser, faVial, faSpinner, faSave, faTimes, faExclamationTriangle,
  faCheckCircle, faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import billingReportsAPI from '../../services/billingReportsAPI';
import { useAuth } from '../../context/AuthContext';
import { useTenant } from '../../context/TenantContext';
import PaginatedTestCards from '../../components/billing/PaginatedTestCards';
import '../../styles/BillingReports.css';
import '../../styles/TestDetailsCard.css';

// PDF generation imports
import jsPDF from 'jspdf';
import QRCode from 'qrcode';
import JsBarcode from 'jsbarcode';

// Try to import autoTable plugin, fallback if not available
let autoTableAvailable = false;
try {
  require('jspdf-autotable');
  autoTableAvailable = true;
} catch (e) {
  console.warn('jspdf-autotable not available, using fallback table generation');
}

// Add inline styles for locked and editable sections
const sectionStyles = `
  .locked-section {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
    opacity: 0.8;
  }

  .editable-section {
    background-color: #f0fff4;
    border: 2px solid #28a745;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
  }

  .locked-section .form-control,
  .locked-section .form-select {
    background-color: #e9ecef;
    cursor: not-allowed;
  }

  .editable-section .form-control,
  .editable-section .form-select {
    border-color: #28a745;
  }

  .editable-section .form-control:focus,
  .editable-section .form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
  }
`;

/**
 * Dedicated billing reports detail page with view and edit modes
 * Supports navigation from samples page and regular billing reports page
 */
const BillingReportsDetail = () => {
  const { sid } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();
  const { currentTenantContext } = useTenant();

  // State management
  const [report, setReport] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [saving, setSaving] = useState(false);
  const [downloadingPDF, setDownloadingPDF] = useState(false);
  const [includeHeader, setIncludeHeader] = useState(true);

  // LOGO INTEGRATION: State for logo base64 data
  const [logoBase64, setLogoBase64] = useState(null);

  // Navigation state
  const [referrer, setReferrer] = useState('billing-reports');

  // Form state for edit mode
  const [editData, setEditData] = useState({});

  // Test results state for editable functionality (commented out as not currently used)
  // const [testResults, setTestResults] = useState({});

  // Sample-specific state for new editable sections
  const [sampleData, setSampleData] = useState({
    sample_info: {
      sample_id: '',
      sample_type: '',
      collection_date: '',
      collection_time: '',
      status: '',
      container_type: '',
      volume: '',
      priority: '',
      collection_notes: ''
    },
    processing_info: {
      received_date: '',
      processing_started: '',
      expected_completion: '',
      technician: '',
      lab_section: '',
      quality_check: '',
      processing_notes: ''
    }
  });

  // Determine referrer from URL parameters or location state
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const fromParam = urlParams.get('from');
    const editParam = urlParams.get('edit');

    // Check URL parameter first, then location state
    if (fromParam === 'samples') {
      setReferrer('samples');
    } else if (location.state?.from === 'samples') {
      setReferrer('samples');
    } else if (location.state?.from === 'billing-reports') {
      setReferrer('billing-reports');
    } else {
      // Default to billing-reports if no referrer is specified
      setReferrer('billing-reports');
    }

    if (editParam === 'true') {
      setEditMode(true);
    }
  }, [location]);

  // Fetch report details and load logo
  useEffect(() => {
    const fetchReport = async () => {
      if (!sid) return;

      try {
        setLoading(true);
        setError(null);

        const response = await billingReportsAPI.getReportBySID(sid);

        if (response.success && response.data) {
          const reportData = response.data.data?.data || response.data.data || response.data;

          if (reportData && typeof reportData === 'object') {
            setReport(reportData);
            setEditData(reportData);

            // Initialize sample data if it exists in the report
            if (reportData.sample_info || reportData.processing_info) {
              setSampleData({
                sample_info: reportData.sample_info || sampleData.sample_info,
                processing_info: reportData.processing_info || sampleData.processing_info
              });
            }
          } else {
            setError('Invalid report data structure received');
          }
        } else {
          setError(response.error || 'Failed to load report details');
        }
      } catch (err) {
        console.error('Error fetching report:', err);
        setError('Failed to load report details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchReport();

    // VERIFY PNG FILE ACCESS: Test if PNG logo file is accessible
    const testLogoAccess = async () => {
      try {
        console.log('=== TESTING PNG LOGO FILE ACCESS ===');
        const response = await fetch('/logoavini.png');
        console.log('PNG logo file fetch response:', {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok,
          url: response.url,
          type: response.type,
          headers: Object.fromEntries(response.headers.entries())
        });

        if (response.ok) {
          const blob = await response.blob();
          console.log('PNG logo file blob:', {
            size: blob.size,
            type: blob.type
          });
          console.log('=== PNG LOGO FILE ACCESS SUCCESSFUL ===');
        } else {
          console.error('PNG logo file not accessible:', response.status, response.statusText);
        }
      } catch (fetchErr) {
        console.error('=== PNG LOGO FILE ACCESS FAILED ===');
        console.error('Fetch error:', fetchErr);
      }
    };

    // Test PNG logo file access first
    testLogoAccess();

    // PNG LOGO LOADING: Direct PNG loading and conversion
    console.log('=== PNG LOGO INITIALIZATION ===');
    convertLogoToBase64().then(base64 => {
      if (base64) {
        setLogoBase64(base64);
        console.log('PNG logo converted to base64 successfully');
        console.log('Logo state updated - base64 length:', base64.length);
        console.log('Logo data starts with:', base64.substring(0, 30));
        console.log('=== PNG LOGO READY FOR PDF GENERATION ===');
      } else {
        console.error('PNG logo conversion failed - base64 is null');
        console.error('Check if logoavini.png exists in public directory');
      }
      console.log('=== END PNG LOGO INITIALIZATION ===');
    }).catch(err => {
      console.error('PNG logo conversion promise rejected:', err);
    });
  }, [sid]);

  // Handle back navigation
  const handleBack = () => {
    if (referrer === 'samples') {
      navigate('/samples');
    } else {
      navigate('/billing/reports');
    }
  };

  // Generate auto Sample ID based on site code and SID number
  const generateSampleId = () => {
    if (!report) return '';

    const siteCode = report.clinic_info?.site_code || 'XX';
    const sidNumber = report.sid_number || '';

    // Format: {SITE_CODE}-{SID_NUMBER} (e.g., "MYD-MYD001")
    return `${siteCode}-${sidNumber}`;
  };

  // Handle edit mode toggle
  const handleEditToggle = () => {
    if (editMode) {
      // Cancel edit - reset form data
      setEditData(report);
      // Reset sample data to initial state
      setSampleData({
        sample_info: {
          sample_id: '',
          sample_type: '',
          collection_date: '',
          collection_time: '',
          status: '',
          container_type: '',
          volume: '',
          priority: '',
          collection_notes: ''
        },
        processing_info: {
          received_date: '',
          processing_started: '',
          expected_completion: '',
          technician: '',
          lab_section: '',
          quality_check: '',
          processing_notes: ''
        }
      });
      setEditMode(false);
    } else {
      // Enter edit mode and auto-generate Sample ID
      const autoGeneratedSampleId = generateSampleId();
      setSampleData(prev => ({
        ...prev,
        sample_info: {
          ...prev.sample_info,
          sample_id: autoGeneratedSampleId
        }
      }));
      setEditMode(true);
    }
  };

  // Handle form input changes for existing data (locked fields)
  const handleInputChange = (field, value) => {
    setEditData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle sample data changes for new editable sections
  const handleSampleDataChange = (section, field, value) => {
    setSampleData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  // Handle save changes
  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      // Validate sample data
      const validationErrors = validateSampleData();
      if (validationErrors.length > 0) {
        setError(`Validation errors: ${validationErrors.join(', ')}`);
        setSaving(false);
        return;
      }

      // Prepare data for saving - combine existing report data with new sample data
      const updatedReportData = {
        ...editData,
        sample_info: sampleData.sample_info,
        processing_info: sampleData.processing_info,
        last_updated: new Date().toISOString(),
        updated_by: 'current_user' // You would get this from auth context
      };

      // Here you would implement the actual save functionality
      // For now, we'll just simulate a save operation
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update the report with new data
      setReport(updatedReportData);
      setEditMode(false);

      // Show success message (you could add a toast notification here)
      console.log('Report updated successfully with sample data:', updatedReportData);
    } catch (err) {
      console.error('Error saving report:', err);
      setError('Failed to save changes. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  // Validate sample data
  const validateSampleData = () => {
    const errors = [];

    // Sample info validation
    if (sampleData.sample_info.sample_id && !sampleData.sample_info.sample_type) {
      errors.push('Sample type is required when sample ID is provided');
    }

    if (sampleData.sample_info.collection_date && !sampleData.sample_info.collection_time) {
      errors.push('Collection time is required when collection date is provided');
    }

    // Processing info validation
    if (sampleData.processing_info.processing_started && !sampleData.processing_info.technician) {
      errors.push('Technician name is required when processing is started');
    }

    return errors;
  };

  // SIMPLIFIED PNG LOGO LOADER: Direct PNG loading without AVIF conversion
  const convertLogoToBase64 = async () => {
    console.log('=== PNG LOGO LOADING START ===');

    try {
      // Create canvas for PNG to base64 conversion
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      // Enable CORS for cross-origin images
      img.crossOrigin = 'anonymous';

      return new Promise((resolve) => {
        img.onload = () => {
          try {
            console.log('=== PNG LOGO LOADED SUCCESSFULLY ===');
            console.log('Logo image properties:', {
              src: img.src,
              width: img.width,
              height: img.height,
              naturalWidth: img.naturalWidth,
              naturalHeight: img.naturalHeight,
              complete: img.complete
            });

            // Validate image dimensions
            if (img.width === 0 || img.height === 0) {
              console.error('Invalid PNG logo dimensions:', img.width, 'x', img.height);
              resolve(null);
              return;
            }

            // Set canvas size to maintain aspect ratio
            canvas.width = img.naturalWidth || img.width;
            canvas.height = img.naturalHeight || img.height;
            console.log('Canvas dimensions set to:', canvas.width, 'x', canvas.height);

            // Clear canvas and draw PNG image
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
            console.log('PNG image drawn to canvas successfully');

            // Convert to base64 PNG (no format conversion needed)
            const base64 = canvas.toDataURL('image/png');
            console.log('=== PNG BASE64 CONVERSION SUCCESSFUL ===');
            console.log('Base64 data properties:', {
              length: base64.length,
              startsWithDataUrl: base64.startsWith('data:image/png;base64,'),
              preview: base64.substring(0, 50) + '...',
              isValid: base64.length > 100 && base64.includes('data:image/png;base64,')
            });

            // Validate base64 data
            if (!base64 || base64.length < 100 || !base64.startsWith('data:image/png;base64,')) {
              console.error('Invalid PNG base64 data generated');
              resolve(null);
              return;
            }

            console.log('=== PNG LOGO CONVERSION COMPLETED SUCCESSFULLY ===');
            resolve(base64);

          } catch (conversionErr) {
            console.error('=== ERROR DURING PNG LOGO CONVERSION ===');
            console.error('PNG conversion error:', conversionErr);
            console.error('Canvas state:', {
              width: canvas.width,
              height: canvas.height,
              context: !!ctx
            });
            resolve(null);
          }
        };

        img.onerror = (err) => {
          console.error('=== PNG LOGO LOAD FAILED ===');
          console.error('PNG load error:', err);
          console.error('Error details:', {
            src: img.src,
            currentSrc: img.currentSrc,
            complete: img.complete,
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight
          });
          resolve(null);
        };

        // LOGO FILE CONFIGURATION: Load PNG logo from public directory
        const logoPath = '/logoavini.png';
        console.log('=== ATTEMPTING TO LOAD PNG LOGO ===');
        console.log('PNG logo path:', logoPath);
        console.log('Full URL:', window.location.origin + logoPath);
        console.log('Current location:', window.location.href);

        img.src = logoPath;

        // Add timeout to detect hanging loads
        setTimeout(() => {
          if (!img.complete) {
            console.warn('PNG logo loading timeout - image not loaded after 5 seconds');
            console.log('PNG image state:', {
              src: img.src,
              complete: img.complete,
              naturalWidth: img.naturalWidth,
              naturalHeight: img.naturalHeight
            });
          }
        }, 5000);
      });

    } catch (err) {
      console.error('=== PNG LOGO FUNCTION ERROR ===');
      console.error('Function error:', err);
      console.log('=== PNG LOGO LOADING END ===');
      return null;
    }
  };

  // Generate QR Code as base64 image
  const generateQRCodeBase64 = async (text) => {
    try {
      return await QRCode.toDataURL(text, {
        width: 100,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
    } catch (err) {
      console.error('QR Code generation error:', err);
      return null;
    }
  };

  // Generate barcode as base64 image
  const generateBarcodeBase64 = (text) => {
    try {
      const canvas = document.createElement('canvas');
      JsBarcode(canvas, text, {
        format: "CODE128",
        displayValue: true,
        width: 2,
        height: 50,
        margin: 5,
        fontSize: 12,
        textMargin: 2
      });
      return canvas.toDataURL('image/png');
    } catch (err) {
      console.error('Barcode generation error:', err);
      return null;
    }
  };

  // Transform actual report data into PDF format - FIXED FOR BILLING REPORT STRUCTURE
  const transformReportDataForPDF = (reportData) => {
    try {
      console.log('Transforming billing report data for PDF:', reportData);

      // Check for the correct data structure - billing reports use 'test_items' not 'billing_items'
      const testItems = reportData.test_items || reportData.billing_items || [];

      if (!reportData || !testItems || testItems.length === 0) {
        console.warn('No test items found in report data, using fallback data');
        console.log('Available report keys:', Object.keys(reportData || {}));
        return getFallbackTestData();
      }

      console.log('Found test items:', testItems);
      const categories = {};

      // Group tests by category/department from actual billing report structure
      testItems.forEach(item => {
        console.log('Processing test item:', item);

        // Extract category from the billing report structure - use department field
        const categoryName = item.department ||
                            item.test_master_data?.department ||
                            item.category ||
                            'GENERAL TESTS';

        if (!categories[categoryName]) {
          categories[categoryName] = {
            category: categoryName,
            tests: []
          };
        }

        // Create test entry with actual billing report data
        const testEntry = {
          name: item.test_name || item.name || 'Unknown Test',
          notes: item.instructions ||
                item.test_master_data?.instructions ||
                item.interpretation ||
                item.test_master_data?.interpretation ||
                '',
          subTests: []
        };

        // Handle sub-tests from billing report structure
        if (item.sub_tests && Array.isArray(item.sub_tests)) {
          item.sub_tests.forEach(subTest => {
            testEntry.subTests.push({
              name: subTest.name || subTest.test_name || subTest.parameter || 'Sub Test',
              result: subTest.result || subTest.value || subTest.test_result || 'Pending',
              unit: subTest.unit || subTest.units || subTest.measurement_unit || '',
              reference: subTest.reference_range || subTest.normal_range || subTest.reference || 'N/A'
            });
          });
        } else {
          // Single test result from billing report - use actual test data
          testEntry.subTests.push({
            name: item.test_name || item.name || 'Test Result',
            result: 'Pending', // Billing reports don't have results yet, they're for ordering
            unit: item.result_unit || item.test_master_data?.result_unit || '',
            reference: item.reference_range || item.test_master_data?.reference_range || 'N/A'
          });
        }

        categories[categoryName].tests.push(testEntry);
        console.log('Added test to category:', categoryName, testEntry);
      });

      // Convert categories object to array
      const transformedData = Object.values(categories);

      console.log('Final transformed data for PDF:', transformedData);
      return transformedData.length > 0 ? transformedData : getFallbackTestData();

    } catch (error) {
      console.error('Error transforming billing report data:', error);
      console.error('Report data structure:', reportData);
      return getFallbackTestData();
    }
  };

  // Fallback test data for when no real data is available
  const getFallbackTestData = () => {
    console.warn('Using fallback test data - no actual test results found in report');
    return [
      {
        category: 'GENERAL TESTS',
        tests: [
          {
            name: 'No Test Data Available',
            notes: 'This PDF was generated but no test results were found in the billing report. Please check the report data structure or contact support.',
            subTests: [
              { name: 'Status', result: 'No Data', unit: '', reference: 'Check Report' }
            ]
          }
        ]
      }
    ];
  };

  // Convert logo image to base64 for PDF embedding
  const getLogoBase64 = () => {
    // AVINI LABS logo in base64 format
    // This is a placeholder - replace with actual logo base64 data
    const logoBase64 = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`;

    return logoBase64;
  };

  // Function to convert image file to base64 (for future logo updates)
  const convertImageToBase64 = (imageFile) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(imageFile);
    });
  };

  // Handle PDF download with professional medical report design - USER-SPECIFIC
  const handleDownloadPDF = async () => {
    if (!report) return;

    try {
      setDownloadingPDF(true);
      setError(null);

      // Log user-specific context for debugging
      console.log('=== PDF GENERATION - OPTIMIZED FORMATTING ===');
      console.log('Current User:', currentUser);
      console.log('Current Tenant:', currentTenantContext);
      console.log('Report Data:', report);
      console.log('Report SID:', report.sid_number);
      console.log('User Role:', currentUser?.role);
      console.log('User Tenant ID:', currentUser?.tenant_id);
      console.log('Report Tenant ID:', report.tenant_id);
      console.log('Formatting Features:');
      console.log('- Compact spacing optimization: ENABLED');
      console.log('- Separate reference range display: ENABLED');
      console.log('- Dynamic signatures: ENABLED');
      console.log('- Fixed page numbering: ENABLED');
      console.log('=== END CONTEXT ===');

      console.log('Starting optimized PDF generation for report:', report.sid_number);

      // Create new jsPDF instance
      const doc = new jsPDF('p', 'mm', 'A4');
      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();

      // FIXED POSITIONING: Always use exact same alignment as "Include header" checked mode
      let yPosition = 20;

      // Generate QR code with direct PDF download URL
      const qrCodeData = `${window.location.origin}/api/billing-reports/sid/${report.sid_number}/pdf`;
      const qrCodeImg = await generateQRCodeBase64(qrCodeData);

      // Generate barcode for SID
      const barcodeImg = generateBarcodeBase64(report.sid_number || 'N/A');

      // Header section - no pink header strip, just maintain consistent spacing
      // No pink header strip in either mode (removed as requested)
      // Always maintain same spacing for consistent alignment
      yPosition = 20; // Same starting position regardless of header visibility

        // LOGO INTEGRATION: Add logo only when header is enabled
        const isFirstPage = doc.internal.getCurrentPageInfo().pageNumber === 1;
        if (logoBase64 && isFirstPage && includeHeader) { // Show logo only when header is enabled
          try {
            // LOGO DEBUGGING: Verify logo data and positioning
            console.log('=== LOGO INTEGRATION DEBUG ===');
            console.log('Logo base64 data available:', !!logoBase64);
            console.log('Logo data length:', logoBase64 ? logoBase64.length : 0);
            console.log('Logo data sample:', logoBase64 ? logoBase64.substring(0, 50) + '...' : 'null');
            console.log('Include header state:', includeHeader);
            console.log('Current page number:', doc.internal.getCurrentPageInfo().pageNumber);
            console.log('Is first page:', isFirstPage);

            // LOGO SIZE ENHANCEMENT: Increased logo size while maintaining positioning
            const logoHeight = 30; // UPDATED: Increased from 15pt to 25pt height
            const logoWidth = logoHeight * 2.5; // Calculate width maintaining 1.5:1 aspect ratio (37.5pt width)
            const logoX = 15; // 15pt from left margin in content area (unchanged)
            const logoY = 10; // 25pt from top (below the 8pt pink header bar) in white content area (unchanged)

            console.log('Enhanced logo positioning coordinates:', { logoX, logoY, logoWidth, logoHeight });
            console.log('Logo size: 25pt height x 37.5pt width (1.5:1 aspect ratio)');

            // jsPDF INTEGRATION: Embed logo using addImage with error handling
            console.log('=== ATTEMPTING jsPDF addImage ===');
            console.log('addImage parameters:', {
              format: 'PNG',
              x: logoX,
              y: logoY,
              width: logoWidth,
              height: logoHeight,
              base64Length: logoBase64.length,
              base64Valid: logoBase64.startsWith('data:image/png;base64,')
            });

            doc.addImage(logoBase64, 'PNG', logoX, logoY, logoWidth, logoHeight);
            console.log('=== jsPDF addImage SUCCESSFUL ===');
            console.log('Enhanced logo added to PDF first page successfully at coordinates (15, 25)');

            // CONTENT SPACING: Updated content adjustment for enlarged logo with 5pt spacing
            yPosition = Math.max(yPosition, logoY + logoHeight + 5); // 25 + 25 + 5 = 55
            console.log('Content yPosition adjusted to:', yPosition, '(accounting for 25pt logo height + 5pt spacing)');
            console.log('=== END LOGO INTEGRATION DEBUG ===');

          } catch (logoErr) {
            console.error('=== LOGO INTEGRATION ERROR ===');
            console.error('Failed to add logo to PDF content area:', logoErr);
            console.error('Logo base64 validation:', {
              exists: !!logoBase64,
              type: typeof logoBase64,
              length: logoBase64 ? logoBase64.length : 0,
              startsWithDataUrl: logoBase64 ? logoBase64.startsWith('data:image/') : false
            });
            console.error('=== END LOGO ERROR ===');
          }
        } else {
          console.log('Logo skipped - Conditions:', {
            logoBase64Available: !!logoBase64,
            includeHeader: includeHeader,
            isFirstPage: isFirstPage,
            currentPage: doc.internal.getCurrentPageInfo().pageNumber
          });
        }

        yPosition += 25;

      // Patient and Report Information Section
      yPosition = generatePatientReportSection(doc, report, yPosition, pageWidth, barcodeImg);

      // Test Results Section with clean list-based format
      const testResultsOutput = generateTestResultsTable(doc, report, yPosition, pageWidth, includeHeader);
      const finalYPosition = testResultsOutput.yPosition || testResultsOutput;
      const totalPages = testResultsOutput.pageCount || 1;

      // QR Code and Signatures Section (Final Page Only)
      try {
        await generateQRCodeAndSignatureSection(doc, qrCodeImg, pageWidth, pageHeight, finalYPosition, totalPages);
      } catch (qrError) {
        console.error('Error in QR code section:', qrError);
        // Add basic fallback QR section
        doc.setFontSize(10);
        doc.setFont('helvetica', 'bold');
        doc.text('QR CODE AND SIGNATURES SECTION', pageWidth / 2, finalYPosition + 20, { align: 'center' });
        doc.text('Dr. Jothi Lakshmi - Verified By', 50, finalYPosition + 40);
        doc.text('Dr. S.Asokkumar - Authorized By', pageWidth - 50, finalYPosition + 40, { align: 'right' });
      }

      // Add user verification footer before saving
      const currentPage = doc.internal.getNumberOfPages();
      doc.setPage(currentPage);

      // Add user-specific generation info at bottom of last page
      doc.setFontSize(7);
      doc.setTextColor(100, 100, 100);
      const generationInfo = `Generated by: ${currentUser?.first_name || ''} ${currentUser?.last_name || ''} (${currentUser?.role || 'User'}) | ${currentTenantContext?.name || 'AVINI LABS'} | ${new Date().toLocaleString()}`;
      doc.text(generationInfo, pageWidth / 2, pageHeight - 10, { align: 'center' });
      doc.setTextColor(0, 0, 0); // Reset color

      // Save the PDF with patient-specific filename
      const patientInfo = report.patient_info || {};
      const patientName = (patientInfo.full_name ||
                          `${patientInfo.first_name || ''} ${patientInfo.last_name || ''}`.trim() ||
                          'Patient').replace(/[^a-zA-Z0-9]/g, '_'); // Clean filename

      const sidNumber = report.sid_number || report.sample_id || 'Report';
      const timestamp = new Date().toISOString().slice(0, 10);
      const filename = `${patientName}_${sidNumber}_${timestamp}.pdf`;

      console.log('Saving patient-specific PDF:', filename);
      console.log('Patient info used for filename:', patientInfo);
      doc.save(filename);

    } catch (err) {
      console.error('Error generating PDF:', err);
      setError(`Failed to generate PDF: ${err.message || 'Unknown error'}. Please try again or contact support.`);
    } finally {
      setDownloadingPDF(false);
    }
  };

  // Generate Patient and Report Information Section
  const generatePatientReportSection = (doc, report, yPos, pageWidth, barcodeImg) => {
    let yPosition = yPos;

    try {
      // CONSISTENT BARCODE POSITIONING: Same position regardless of header setting for preprinted paper compatibility
      if (barcodeImg) {
        doc.addImage(barcodeImg, 'PNG', pageWidth - 55, yPosition - 15, 45, 15);
      }

      // Patient Information (Left Column) - improved readability
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(10); // Increased font size for better readability
      doc.setFont('helvetica', 'normal');

      // Professional column positioning with adequate spacing
      const labelX = 15;
      const colonX = 50; // More space for labels
      const valueX = 55; // Clear separation from colon

      // Left-aligned labels with proper spacing
      doc.text('Patient', labelX, yPosition);
      doc.text('Age / Sex', labelX, yPosition + 6);
      doc.text('Patient ID', labelX, yPosition + 12);
      doc.text('Branch', labelX, yPosition + 18);

      // Properly aligned colons
      doc.text(':', colonX, yPosition);
      doc.text(':', colonX, yPosition + 6);
      doc.text(':', colonX, yPosition + 12);
      doc.text(':', colonX, yPosition + 18);

      // Extract actual patient data from billing report - FIXED STRUCTURE
      console.log('Extracting patient data from billing report:', report.patient_info);
      console.log('Full report structure:', report);

      // Handle the actual billing report structure
      const patientInfo = report.patient_info || {};
      const patientName = patientInfo.full_name ||
                         patientInfo.name ||
                         `${patientInfo.first_name || ''} ${patientInfo.last_name || ''}`.trim() ||
                         'N/A';

      const patientAge = patientInfo.age ||
                        (patientInfo.date_of_birth ?
                         Math.floor((new Date() - new Date(patientInfo.date_of_birth)) / (365.25 * 24 * 60 * 60 * 1000)) :
                         'N/A');

      const patientGender = patientInfo.gender || 'N/A';
      const patientId = patientInfo.patient_id || patientInfo.id || 'N/A';

      // Extract clinic/branch information from billing report
      const clinicInfo = report.clinic_info || {};
      const branchName = clinicInfo.name ||
                        clinicInfo.branch_name ||
                        currentTenantContext?.name ||
                        currentUser?.first_name ||
                        'AVINI LABS';

      console.log('Extracted patient data for PDF:', {
        name: patientName,
        age: patientAge,
        gender: patientGender,
        id: patientId,
        branch: branchName
      });

      // Consistently positioned values with ACTUAL USER DATA
      doc.text(patientName, valueX, yPosition);
      doc.text(`${patientAge} / ${patientGender}`, valueX, yPosition + 6);
      doc.text(patientId.toString(), valueX, yPosition + 12);
      doc.text(branchName, valueX, yPosition + 18);

      // Report Information (Right Column) - properly aligned to avoid barcode overlap
      const rightLabelX = 120;
      const rightColonX = 155; // Consistent colon alignment
      const rightValueX = 160; // Clear positioning after colon

      // Left-aligned labels - clean vertical stack
      doc.text('SID No.', rightLabelX, yPosition);
      doc.text('Reg Date & Time', rightLabelX, yPosition + 6);
      doc.text('Coll Date & Time', rightLabelX, yPosition + 12);
      doc.text('Report Date & Time', rightLabelX, yPosition + 18);

      // Properly aligned colons - consistent positioning
      doc.text(':', rightColonX, yPosition);
      doc.text(':', rightColonX, yPosition + 6);
      doc.text(':', rightColonX, yPosition + 12);
      doc.text(':', rightColonX, yPosition + 18);

      // Extract actual dates from billing report - FIXED STRUCTURE
      const sampleId = report.sid_number || report.sample_id || report.id || 'N/A';

      // Use billing report specific date fields
      const regDate = report.registration_date ||
                     report.billing_date ||
                     report.invoice_date ||
                     report.created_at ||
                     new Date().toISOString();

      const collDate = report.collection_date ||
                      report.sample_collection_date ||
                      report.created_at ||
                      new Date().toISOString();

      const reportDate = report.reported_date ||
                        report.report_date ||
                        report.updated_at ||
                        new Date().toISOString();

      console.log('Extracted dates for PDF:', {
        sampleId,
        regDate,
        collDate,
        reportDate
      });

      // Format dates properly for display
      const formatDate = (dateStr) => {
        try {
          const date = new Date(dateStr);
          return date.toLocaleString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }).replace(',', '');
        } catch {
          return 'N/A';
        }
      };

      // Consistently positioned values with ACTUAL USER DATA
      doc.text(sampleId.toString(), rightValueX, yPosition);
      doc.text(formatDate(regDate), rightValueX, yPosition + 6);
      doc.text(formatDate(collDate), rightValueX, yPosition + 12);
      doc.text(formatDate(reportDate), rightValueX, yPosition + 18);

      // Add user context information for debugging
      console.log('PDF generated with user-specific data:', {
        user: currentUser?.username,
        tenant: currentTenantContext?.name,
        patient: patientName,
        sid: sampleId,
        reportDate: formatDate(reportDate)
      });

    } catch (err) {
      console.error('Error in generatePatientReportSection:', err);
    }

    return yPosition + 25; // Proper spacing without excessive gaps
  };

  // ENHANCED FOOTER: Branch information above 8pt pink footer bar
  const addPersistentFooter = (doc, currentPageWidth, currentPageHeight, currentPage = 1, totalPages = 1) => {
    // FOOTER CONTENT ENHANCEMENT: Branch locations text above pink footer bar
    const branchTextY = currentPageHeight - 16; // Position for branch text in white area

    // HEAD OFFICE POSITIONING: Above branches on left side
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(236, 72, 153); // Black text in white area
    doc.text('Head Office', 15, branchTextY - 5);

    // BRANCH TEXT CONTENT: Display branch locations list
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(0, 0, 0); // Black text in white area
    const branchText = 'Mayiladuthurai | Chidambaram | Sirkazhi | Sankanankovil | Kumbakonam | Pandanallur | Thirupanandal | Eravanchery | Nannilam | Thanjavur | Needamangalam | Thiruthuraipoondi | Tiruvarur | Avadi | Ambakkam';

    // BRANCH TEXT STYLING: Centered alignment with proper wrapping
    const wrappedBranchLines = doc.splitTextToSize(branchText, currentPageWidth - 30);
    wrappedBranchLines.forEach((line, index) => {
      doc.text(line, currentPageWidth / 2, branchTextY + (index * 4), { align: 'center' });
    });

    // SPACING: 5-10pt gap between branch text and pink footer bar
    const pinkFooterY = currentPageHeight - 8;
    doc.setFillColor(236, 72, 153); // Same pink color as header for consistency
    doc.rect(0, pinkFooterY, currentPageWidth, 8, 'F'); // 8pt height footer bar

    // FOOTER TEXT ENHANCEMENT: Head Office text on left side of pink bar
    doc.setFontSize(8);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(255, 255, 255); // White text on pink background
    // doc.text('Head Office', 15, pinkFooterY + 5); // Positioned for 8pt footer height

    // FOOTER CONTENT: Contact information in center (adjusted for 8pt height)
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(255, 255, 255); // White text on pink background
    const contactInfo = 'Customer Care No: 1800 572 4455';
    doc.text(contactInfo, currentPageWidth / 2, pinkFooterY + 5, { align: 'center' });

    // FOOTER PAGINATION: Page numbering on right side (adjusted for 8pt height)
    doc.setFontSize(7);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(255, 255, 255); // White text on pink background
    doc.text(`Page ${currentPage} of ${totalPages}`, currentPageWidth - 15, pinkFooterY + 5, { align: 'right' });

    console.log('Enhanced footer with branches added:', {
      footerHeight: '8pt',
      branchTextPosition: branchTextY,
      pinkFooterPosition: pinkFooterY,
      branchTextLines: wrappedBranchLines.length,
      colorConsistency: 'Matches header pink (236, 72, 153)'
    });
  };

  // Generate Test Results Section - Clean List-Based Format
  const generateTestResultsTable = (doc, reportData, yPos, pageWidth, includeHeader = true) => {
    let yPosition = yPos;
    let pageCount = 1;
    let actualPageCount = 1;

    try {
      // Get page dimensions from the jsPDF document instance
      const pageHeight = doc.internal.pageSize.getHeight();

      // FINAL TEST REPORT header section with uniform formatting
      doc.setDrawColor(0, 0, 0);
      doc.setLineWidth(1.2);
      doc.line(10, yPosition, pageWidth - 10, yPosition);
      yPosition += 8;

      // "FINAL TEST REPORT" text - centered and bold
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(0, 0, 0);
      doc.text('FINAL TEST REPORT', pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 6;

      // Bottom line below "FINAL TEST REPORT" with uniform formatting
      doc.line(10, yPosition, pageWidth - 10, yPosition);
      yPosition += 15;

      // FOOTER HEIGHT ADJUSTMENT: Updated bottom margin for enhanced footer with branch text
      const bottomMargin = 35; // Space for 8pt footer + branch text above (was 20 for simple footer)
      const maxContentHeight = pageHeight - bottomMargin;

      // Function to add column headers
      const addColumnHeaders = (currentY) => {
        doc.setFontSize(9);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(0, 0, 0);

        // Column headers with proper spacing
        const investigationX = 25;
        const resultX = 115;
        const unitsX = 145;
        const referenceX = 170;

        doc.text('INVESTIGATION / METHOD', investigationX, currentY);
        doc.text('RESULT', resultX, currentY);
        doc.text('UNITS', unitsX, currentY);
        doc.text('REFERENCE INTERVAL', referenceX, currentY);

        // Add a line under headers with uniform formatting
        doc.setDrawColor(0, 0, 0);
        doc.setLineWidth(1.2);
        doc.line(10, currentY + 2, pageWidth - 10, currentY + 2);

        return currentY + 10; // Increased spacing after headers for better separation
      };

      // OPTIMIZED function to determine if reference range should be displayed separately
      const isComplexReferenceRange = (referenceText) => {
        if (!referenceText || referenceText.trim() === '') return false;

        // Consider it complex if it's longer than 30 characters or contains multiple segments
        const cleanText = referenceText.trim();
        const hasMultipleSegments = cleanText.includes(':') && cleanText.length > 30;
        const hasAgeGroups = /\b(month|year|adult|child|male|female|born)\b/i.test(cleanText);
        const isLong = cleanText.length > 40;

        return hasMultipleSegments || hasAgeGroups || isLong;
      };

      // Enhanced function to format reference ranges for separate display
      const formatReferenceRangeForDisplay = (referenceText, maxWidth = 120) => {
        if (!referenceText || referenceText.trim() === '') return ['N/A'];

        console.log('Formatting complex reference range for display:', referenceText);

        // Clean up the text and handle different delimiters
        let cleanText = referenceText.replace(/\n+/g, ' ').trim();

        // Split by common medical reference range patterns
        const segments = [];

        // Handle age-based ranges (common in medical tests)
        if (cleanText.includes('month') || cleanText.includes('year') || cleanText.includes('Adult')) {
          // Split by age groups and conditions
          const ageSegments = cleanText.split(/(?=\d+\s*(?:month|year)|Adult|Child|Male|Female|New Born|Cord Blood)/i);
          ageSegments.forEach(segment => {
            const trimmed = segment.trim();
            if (trimmed) segments.push(trimmed);
          });
        } else {
          // Split by other common delimiters
          const splitSegments = cleanText.split(/\s*[:|;]\s*(?=[A-Z]|\d)/)
            .filter(segment => segment.trim() !== '');
          segments.push(...splitSegments);
        }

        // Format each segment with proper line breaks
        const formattedLines = [];
        segments.forEach(segment => {
          const trimmedSegment = segment.trim();
          if (trimmedSegment) {
            // Split long segments if needed
            const splitSegment = doc.splitTextToSize(trimmedSegment, maxWidth);
            formattedLines.push(...splitSegment);
          }
        });

        console.log('Formatted reference lines:', formattedLines);
        return formattedLines.length > 0 ? formattedLines : ['N/A'];
      };

      // OPTIMIZED function to calculate test section height with compact spacing
      const calculateTestHeight = (test) => {
        let height = 5; // Reduced base height for test name (more compact)

        // Calculate height for each sub-test with optimized spacing
        test.subTests.forEach(subTest => {
          height += 4; // Reduced sub-test height for compactness

          // Add height for complex reference ranges that will be displayed separately
          if (isComplexReferenceRange(subTest.reference)) {
            const referenceLines = formatReferenceRangeForDisplay(subTest.reference);
            height += 2 + (referenceLines.length * 2.5) + 2; // Compact reference section
          }
        });

        // Calculate height for notes with optimized spacing
        if (test.notes && test.notes.trim()) {
          const noteLines = doc.splitTextToSize(`Notes: ${test.notes}`, pageWidth - 50);
          height += 2 + (noteLines.length * 2.5) + 2; // More compact notes spacing
        } else {
          height += 2; // Minimal spacing when no notes
        }

        return height;
      };

      // OPTIMIZED function to check if page break is needed with compact spacing
      const checkPageBreak = (currentY, requiredSpace = 20) => {
        if (currentY + requiredSpace > maxContentHeight) {
          pageCount++;

          // Create new page (footer will be added at the end with correct page numbers)
          doc.addPage();

          // CONSISTENT POSITIONING: No pink header strip on new pages, just maintain positioning
          // No pink header strip in either mode (removed as requested)
          let newPageY = 20; // Consistent positioning for all pages

          // CONDITIONAL COLUMN HEADERS: Re-add column headers on new page only if valid test data exists
          if (shouldDisplayHeaders) {
            newPageY = addColumnHeaders(newPageY);
            console.log('Column headers re-added on new page - valid test data exists');
          } else {
            console.log('Column headers skipped on new page - no valid test data');
          }

          // Add small buffer after headers for better visual separation
          newPageY += 2;

          return newPageY;
        }
        return currentY;
      };



      // Transform actual report data for PDF generation
      console.log('Using actual report data for PDF generation:', reportData);
      const actualTestData = transformReportDataForPDF(reportData);
      console.log('Transformed test data:', actualTestData);

      // ENHANCED TEST DATA DETECTION: Comprehensive validation for header display
      const hasActualTestData = reportData.test_items &&
                               Array.isArray(reportData.test_items) &&
                               reportData.test_items.length > 0;

      // ADDITIONAL VALIDATION: Check if transformed data contains actual tests
      const hasTransformedTestData = actualTestData &&
                                   Array.isArray(actualTestData) &&
                                   actualTestData.length > 0 &&
                                   actualTestData.some(category =>
                                     category.tests &&
                                     Array.isArray(category.tests) &&
                                     category.tests.length > 0
                                   );

      // FINAL TEST DATA DETERMINATION: Both original and transformed data must be valid
      const shouldDisplayHeaders = hasActualTestData && hasTransformedTestData;

      console.log('Enhanced test data detection:', {
        hasTestItems: !!reportData.test_items,
        isArray: Array.isArray(reportData.test_items),
        itemCount: reportData.test_items ? reportData.test_items.length : 0,
        hasActualTestData: hasActualTestData,
        hasTransformedTestData: hasTransformedTestData,
        shouldDisplayHeaders: shouldDisplayHeaders,
        transformedDataStructure: actualTestData ? actualTestData.map(cat => ({
          category: cat.category,
          testCount: cat.tests ? cat.tests.length : 0
        })) : 'No transformed data'
      });

      // Use actual test data instead of hardcoded sample data
      const dynamicTests = actualTestData;

      // CONDITIONAL SECTION TITLE AND HEADERS: Only show when valid test data exists
      if (shouldDisplayHeaders) {
        // Add section title with professional header formatting
        yPosition += 10; // Space before section

        // Add horizontal line above "INVESTIGATION REPORT" - full page width with uniform formatting
        doc.setDrawColor(0, 0, 0);
        doc.setLineWidth(1.2);
        doc.line(10, yPosition, pageWidth - 10, yPosition);
        yPosition += 5; // Reduced space after top line

        doc.setFont('helvetica', 'bold');
        doc.setFontSize(13);
        doc.setTextColor(0, 0, 0);
        doc.text('INVESTIGATION REPORT', pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 5; // Reduced space after title

        // Add horizontal line below "INVESTIGATION REPORT" - full page width with uniform formatting
        doc.setDrawColor(0, 0, 0);
        doc.setLineWidth(1.2);
        doc.line(10, yPosition, pageWidth - 10, yPosition);
        yPosition += 10; // Space after bottom line

        // CONDITIONAL COLUMN HEADERS: Only add when test data exists
        yPosition = addColumnHeaders(yPosition);
        console.log('Section title and column headers added - valid test data detected');
      } else {
        console.log('Section title and column headers suppressed - no valid test data detected');
        yPosition += 5; // Minimal spacing when no test section
      }

      // CONDITIONAL TEST RESULTS RENDERING: Only render if valid test data exists
      if (shouldDisplayHeaders && dynamicTests && dynamicTests.length > 0) {
        console.log('Rendering test results - valid test data detected');

        // OPTIMIZED test results rendering with compact spacing and separate reference display
        dynamicTests.forEach((category, categoryIndex) => {
        // Check if we need a page break for category header
        yPosition = checkPageBreak(yPosition, 15);

        // OPTIMIZED Category header with compact spacing
        doc.setFont('helvetica', 'bold');
        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        doc.text(category.category, 15, yPosition);
        yPosition += 5; // Reduced spacing after category header

        category.tests.forEach((test) => {
          // Use optimized height calculation
          const estimatedTestHeight = calculateTestHeight(test);

          // Check if we need a page break for the entire test section
          yPosition = checkPageBreak(yPosition, estimatedTestHeight);

          // OPTIMIZED Test name with compact spacing
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(11);
          doc.setTextColor(0, 0, 0);
          doc.text(test.name, 20, yPosition);
          yPosition += 4; // Reduced spacing after test name

          // Sub-tests with OPTIMIZED spacing
          test.subTests.forEach((subTest) => {
            // Check page break for each sub-test
            yPosition = checkPageBreak(yPosition, 4);

            doc.setFont('helvetica', 'normal');
            doc.setFontSize(9);

            // Clean text alignment - professional medical report style
            doc.text(subTest.name, 25, yPosition);
            doc.text(subTest.result, 115, yPosition);
            doc.text(subTest.unit, 145, yPosition);

            // OPTIMIZED: Simple reference display in column for simple ranges
            if (!isComplexReferenceRange(subTest.reference)) {
              // Simple reference range fits in column
              const simpleRef = subTest.reference || 'N/A';
              doc.text(simpleRef.length > 25 ? simpleRef.substring(0, 22) + '...' : simpleRef, 170, yPosition);
            } else {
              // Complex reference will be displayed separately below
              doc.text('See below', 170, yPosition);
            }

            yPosition += 4; // Compact spacing for sub-tests
          });

          // ENHANCED: Display complex reference ranges separately (like notes)
          test.subTests.forEach((subTest) => {
            if (isComplexReferenceRange(subTest.reference)) {
              yPosition += 1; // Small gap before reference section

              // Reference range header
              doc.setFontSize(8);
              doc.setFont('helvetica', 'bold');
              doc.setTextColor(80, 80, 80);
              doc.text(`Reference Range (${subTest.name}):`, 25, yPosition);
              yPosition += 2.5;

              // Format and display reference range
              doc.setFont('helvetica', 'italic');
              doc.setFontSize(8);
              doc.setTextColor(60, 60, 60);

              const referenceLines = formatReferenceRangeForDisplay(subTest.reference);
              referenceLines.forEach((line) => {
                yPosition = checkPageBreak(yPosition, 3);
                doc.text(line, 30, yPosition);
                yPosition += 2.5; // Compact line spacing
              });

              // Reset formatting
              doc.setFontSize(9);
              doc.setFont('helvetica', 'normal');
              doc.setTextColor(0, 0, 0);
              yPosition += 1; // Small gap after reference section
            }
          });

          // OPTIMIZED notes section with compact spacing
          if (test.notes && test.notes.trim()) {
            // Compact spacing before notes
            yPosition += 1;

            // NOTES FONT SIZE REDUCTION: 8pt font for compact appearance
            doc.setFontSize(8); // Reduced from 10pt to 8pt font size
            doc.setFont('helvetica', 'normal');
            doc.setTextColor(0, 0, 0);

            // Split notes into lines with optimized width
            const noteLines = doc.splitTextToSize(`Notes: ${test.notes}`, pageWidth - 60);

            // Render notes with compact spacing
            noteLines.forEach((line) => {
              yPosition = checkPageBreak(yPosition, 3);
              doc.text(line, 25, yPosition);
              yPosition += 3; // Reduced line spacing for 8pt font
            });

            // TYPOGRAPHY CONSISTENCY: Reset formatting to standard values
            doc.setFontSize(11); // Reset to standard table font size
            doc.setFont('helvetica', 'normal');
            doc.setTextColor(0, 0, 0);
            yPosition += 5; // Reduced space after notes for compact layout
          } else {
            // Minimal spacing when no notes are present
            yPosition += 2;
          }
        });

        // OPTIMIZED category spacing system for compact layout
        const isLastCategory = categoryIndex === dynamicTests.length - 1;

        if (!isLastCategory) {
          // Compact spacing before separator
          yPosition += 3;
          yPosition = checkPageBreak(yPosition, 5);

          // Optimized horizontal separator line with uniform formatting
          doc.setDrawColor(0, 0, 0);
          doc.setLineWidth(1.2);
          doc.line(10, yPosition, pageWidth - 10, yPosition);

          // Compact spacing after separator
          yPosition += 4;
        } else {
          // Minimal final spacing for last category
          yPosition += 3;
        }
        });

      } else {
        // NO VALID TEST DATA SCENARIO: Content flow directly to signature section
        console.log('No valid test data detected - skipping test results rendering');
        console.log('Test data validation failed:', {
          hasActualTestData: hasActualTestData,
          hasTransformedTestData: hasTransformedTestData,
          shouldDisplayHeaders: shouldDisplayHeaders
        });
        yPosition += 10; // Minimal spacing when no test data
      }

      // END OF REPORT section with optimized spacing
      yPosition += 6; // Further reduced for compactness

      // Calculate space needed for END OF REPORT + QR/signature section
      const endOfReportHeight = 12; // Reduced text + spacing
      const signatureSectionHeight = 40; // Reduced QR code + signatures height
      const totalEndSectionHeight = endOfReportHeight + signatureSectionHeight;
      const minSpacingBeforeSignatures = 20; // Further reduced for compactness

      // Only add page break if there's truly insufficient space
      if (yPosition + totalEndSectionHeight + minSpacingBeforeSignatures > maxContentHeight) {
        pageCount++;
        doc.addPage();

        // CONDITIONAL HEADER: Show header only when enabled, but maintain consistent positioning
        if (includeHeader) {
          // Show pink header bar on new pages when header is enabled
          doc.setFillColor(236, 72, 153);
          doc.rect(0, 0, pageWidth, 8, 'F');
        }
        yPosition = 20; // Consistent positioning for all pages
      }

      // Add "END OF REPORT" text
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(0, 0, 0);
      doc.text('END OF REPORT', pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 12; // Reduced for compactness

      // CONDITIONAL FOOTER: Add footer only when header is enabled
      actualPageCount = doc.internal.getNumberOfPages();

      if (includeHeader) {
        // Update all page footers with correct total page count when header is enabled
        for (let i = 1; i <= actualPageCount; i++) {
          doc.setPage(i);
          addPersistentFooter(doc, pageWidth, pageHeight, i, actualPageCount);
        }
        console.log('Footer added to all pages (header mode enabled)');
      } else {
        console.log('Footer skipped (header mode disabled - for preprinted papers)');
      }

    } catch (err) {
      console.error('Error in generateTestResultsTable:', err);

      // Fallback rendering
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(0, 0, 0);
      doc.text('PDF Generation Error - Fallback Mode', 20, yPosition);
      yPosition += 15;

      actualPageCount = doc.internal.getNumberOfPages();

      // CONDITIONAL FOOTER: Update footers even in error case only when header is enabled
      if (includeHeader) {
        const errorPageHeight = doc.internal.pageSize.getHeight();
        for (let i = 1; i <= actualPageCount; i++) {
          doc.setPage(i);
          addPersistentFooter(doc, pageWidth, errorPageHeight, i, actualPageCount);
        }
        console.log('Error handling: Footer added to all pages (header mode enabled)');
      } else {
        console.log('Error handling: Footer skipped (header mode disabled)');
      }
    }

    return { yPosition: yPosition, pageCount: actualPageCount };
  };

  // Load signature image function with background removal
  const loadSignatureImage = () => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = img.width;
        canvas.height = img.height;

        // Fill with transparent background
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw image
        ctx.drawImage(img, 0, 0);

        // Convert to PNG to preserve transparency
        resolve(canvas.toDataURL('image/png'));
      };
      img.onerror = () => {
        console.warn('Signature image not found, using text-based signature');
        resolve(null);
      };
      img.src = '/signature.jpeg';
    });
  };

  // Generate QR Code and Signature Section (Final Page Only)
  const generateQRCodeAndSignatureSection = async (doc, qrCodeImg, pageWidth, pageHeight, contentEndY = 0, totalPages = 1) => {
    try {
      // Load signature image
      const signatureImg = await loadSignatureImage();

      console.log('🔖 Generating QR code and signature section at Y position:', contentEndY);
      console.log('🔖 Total pages for numbering:', totalPages);

      // OPTIMIZED positioning - use available space efficiently
      const minBottomMargin = 50; // Reduced from 100 for better space utilization
      const availableSpace = pageHeight - contentEndY - minBottomMargin;
      const signatureSectionHeight = 45; // Actual height needed for signatures

      // Position signatures optimally - either right after content or at bottom if space is tight
      const signatureY = availableSpace >= signatureSectionHeight
        ? contentEndY + 10  // Place right after content if space allows
        : pageHeight - minBottomMargin - signatureSectionHeight; // Otherwise position at bottom

      console.log('🔖 Optimized signature Y position:', signatureY);
      console.log('🔖 Available space:', availableSpace);
      console.log('🔖 Signature section height needed:', signatureSectionHeight);

      // Debug: Add a visible marker to verify function is being called
      console.log('🔖 Adding signature section elements...');

      // Signatures section with improved readability
      doc.setFontSize(9);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(0, 0, 0);

      // DYNAMIC Left signature - "Verified By" section with user-specific information
      console.log('🔖 Adding dynamic left signature...');
      doc.setTextColor(0, 0, 0);
      doc.setDrawColor(100, 100, 100);
      doc.setLineWidth(0.5);
      doc.line(15, signatureY + 5, 75, signatureY + 5);

      doc.setFont('helvetica', 'bold');
      doc.setFontSize(9);
      doc.text('Verified By', 45, signatureY + 15, { align: 'center' });

      // Use dynamic user information for verification signature
      const verifierName = currentUser?.role === 'lab_technician' || currentUser?.role === 'technician'
        ? `${currentUser.first_name || ''} ${currentUser.last_name || ''}`.trim()
        : currentUser?.first_name && currentUser?.last_name
          ? `${currentUser.first_name} ${currentUser.last_name}`
          : 'Lab Technician';

      const verifierRole = currentUser?.role === 'lab_technician' ? 'Lab Technician'
        : currentUser?.role === 'technician' ? 'Medical Technician'
        : currentUser?.role === 'doctor' ? 'Medical Officer'
        : 'Lab Technician';

      doc.text(verifierName, 45, signatureY + 22, { align: 'center' });
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(8);
      doc.text(verifierRole, 45, signatureY + 28, { align: 'center' });
      console.log('🔖 Dynamic left signature added:', verifierName, verifierRole);

      // QR Code positioned in center with enhanced visibility
      console.log('🔖 Adding QR code...', qrCodeImg ? 'QR code available' : 'No QR code');
      if (qrCodeImg) {
        const qrSize = 30; // Increased size for better visibility
        const qrX = (pageWidth / 2) - (qrSize / 2);
        const qrY = signatureY - 10; // Better positioning

        // Add white background for QR code
        doc.setFillColor(255, 255, 255);
        doc.rect(qrX - 2, qrY - 2, qrSize + 4, qrSize + 4, 'F');

        doc.addImage(qrCodeImg, 'PNG', qrX, qrY, qrSize, qrSize);
        console.log('🔖 QR code added at position:', qrX, qrY, 'size:', qrSize);
      } else {
        // Add more visible placeholder if QR code is not available
        doc.setFontSize(10);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(100, 100, 100);
        doc.text('QR CODE', pageWidth / 2, signatureY + 5, { align: 'center' });
        doc.setFontSize(8);
        doc.setFont('helvetica', 'normal');
        doc.text('(Report Verification)', pageWidth / 2, signatureY + 12, { align: 'center' });
        console.log('🔖 QR code placeholder added with enhanced visibility');
      }

      // Right signature area with enhanced professional formatting - signature above line
      console.log('🔖 Adding right signature...');
      const rightSigX = pageWidth - 15; // Better margin

      // DYNAMIC authorization signature based on user context and tenant
      const authorizerName = currentUser?.role === 'admin' || currentUser?.role === 'manager'
        ? `${currentUser.first_name || ''} ${currentUser.last_name || ''}`.trim()
        : currentTenantContext?.manager_name || 'Dr. S.Asokkumar, PhD.';

      const authorizerTitle = currentUser?.role === 'admin' ? 'Laboratory Administrator'
        : currentUser?.role === 'manager' ? 'Laboratory Manager'
        : currentUser?.role === 'doctor' ? 'Medical Director'
        : currentTenantContext?.manager_title || 'Clinical Microbiologist & QM';

      // DOCTOR NAME: First display the doctor name at the top
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      doc.text(authorizerName, rightSigX, signatureY - 10, { align: 'right' });

      // SIGNATURE: Position signature above the line
      if (signatureImg) {
        // Add actual signature image above the line
        const sigWidth = 40;
        const sigHeight = 15;
        const sigX = rightSigX - 35 - (sigWidth / 2);
        doc.addImage(signatureImg, 'PNG', sigX, signatureY - 5, sigWidth, sigHeight);
      } else {
        // Fallback to text-based signature above the line
        doc.setFont('helvetica', 'italic');
        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        doc.text('_________________', rightSigX - 35, signatureY, { align: 'center' });
      }

      // LINE: Position line below signature
      doc.setDrawColor(100, 100, 100); // Darker line for better visibility
      doc.setLineWidth(0.5);
      doc.line(rightSigX - 70, signatureY + 5, rightSigX, signatureY + 5);

      // "AUTHORIZED BY" TEXT: Position below the line
      doc.setFontSize(9);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(0, 0, 0);
      doc.text('Authorized By', rightSigX, signatureY + 15, { align: 'right' });

      console.log('Professional signature positioning:', {
        doctorName: authorizerName,
        doctorNameY: signatureY - 10,
        signatureY: signatureY,
        referenceLineY: signatureY + 5,
        positioning: 'signature above reference line, above Authorized By'
      });

      // TITLE: Below "Authorized By" text
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(8);
      doc.text(authorizerTitle, rightSigX, signatureY + 22, { align: 'right' });
      console.log('🔖 Dynamic right signature added:', authorizerName, authorizerTitle);

      // Note: Footer will be added at the end with correct page numbering
      console.log('🔖 QR code and signature section completed successfully');
      console.log('🔖 Final signature Y position:', signatureY);
      console.log('🔖 All elements should now be visible in PDF');

    } catch (err) {
      console.error('❌ Error in generateQRCodeAndSignatureSection:', err);
      console.error('❌ Error stack:', err.stack);
      console.error('❌ This may cause QR code and signatures to not appear');
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="billing-reports-detail-container">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
            Billing Report Details
          </h1>
          <Button variant="secondary" onClick={handleBack}>
            <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
            Back
          </Button>
        </div>
        <Alert variant="danger">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      </div>
    );
  }

  // No report found
  if (!report) {
    return (
      <div className="billing-reports-detail-container">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1 className="h3 mb-0 text-gray-800">
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
            Billing Report Details
          </h1>
          <Button variant="secondary" onClick={handleBack}>
            <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
            Back
          </Button>
        </div>
        <Alert variant="warning">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          Report not found for SID: {sid}
        </Alert>
      </div>
    );
  }

  return (
    <div className="billing-reports-detail-container">
      {/* Inject custom styles */}
      <style>{sectionStyles}</style>

      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="h3 mb-0 text-gray-800">
          <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
          Billing Report Details
          <Badge bg="primary" className="ms-2">{report.sid_number}</Badge>
        </h1>
        <div>
          <Button variant="secondary" onClick={handleBack} className="me-2">
            <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
            Back
          </Button>
          {editMode ? (
            <>
              <Button 
                variant="success" 
                onClick={handleSave} 
                disabled={saving}
                className="me-2"
              >
                {saving ? (
                  <>
                    <FontAwesomeIcon icon={faSpinner} spin className="me-1" />
                    Saving...
                  </>
                ) : (
                  <>
                    <FontAwesomeIcon icon={faSave} className="me-1" />
                    Save Changes
                  </>
                )}
              </Button>
              <Button variant="outline-secondary" onClick={handleEditToggle}>
                <FontAwesomeIcon icon={faTimes} className="me-1" />
                Cancel
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline-primary" onClick={handleEditToggle} className="me-2">
                <FontAwesomeIcon icon={faEdit} className="me-1" />
                Edit Report
              </Button>
              <Button
                variant="success"
                onClick={handleDownloadPDF}
                disabled={downloadingPDF}
              >
                {downloadingPDF ? (
                  <>
                    <FontAwesomeIcon icon={faSpinner} spin className="me-1" />
                    Downloading...
                  </>
                ) : (
                  <>
                    <FontAwesomeIcon icon={faDownload} className="me-1" />
                    Download PDF
                  </>
                )}
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="danger" className="mb-4">
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}

      {/* PDF Options - Only show when not in edit mode */}
      {!editMode && (
        <Card className="mb-4">
          <Card.Body>
            <h6 className="text-primary mb-3">
              <FontAwesomeIcon icon={faDownload} className="me-2" />
              PDF Download Options
            </h6>
            <Row>
              <Col md={6}>
                <Form.Check
                  type="checkbox"
                  id="include-header-checkbox"
                  label="Include Header in PDF"
                  checked={includeHeader}
                  onChange={(e) => setIncludeHeader(e.target.checked)}
                  className="mb-2"
                />
                <small className="text-muted">
                  When checked, the PDF will include the AVINI LABS header with logo and company information.
                  When unchecked, the PDF will start directly with patient and test information.
                </small>
              </Col>
              <Col md={6}>
                <div className="d-flex align-items-center mb-3">
                  <FontAwesomeIcon icon={faInfoCircle} className="text-info me-2" />
                  <small className="text-muted">
                    The PDF includes a QR code that links directly to this report for easy access and verification.
                  </small>
                </div>
                <Button
                  variant="outline-info"
                  size="sm"
                  onClick={() => {
                    console.log('=== BILLING REPORT PDF DEBUG INFO ===');
                    console.log('Current User:', currentUser);
                    console.log('Current Tenant:', currentTenantContext);
                    console.log('Full Report Data:', report);
                    console.log('Report Keys:', Object.keys(report || {}));
                    console.log('Patient Info:', report?.patient_info);
                    console.log('Test Items:', report?.test_items);
                    console.log('Billing Items:', report?.billing_items);
                    console.log('Clinic Info:', report?.clinic_info);
                    console.log('Financial Summary:', report?.financial_summary);
                    console.log('SID Number:', report?.sid_number);
                    console.log('PDF Libraries Status:');
                    console.log('- jsPDF available:', typeof jsPDF !== 'undefined');
                    console.log('- QRCode available:', typeof QRCode !== 'undefined');
                    console.log('- JsBarcode available:', typeof JsBarcode !== 'undefined');
                    console.log('- autoTable available:', autoTableAvailable);
                    console.log('User Access Level:', currentUser?.role);
                    console.log('User Tenant ID:', currentUser?.tenant_id);
                    console.log('Report Tenant ID:', report?.tenant_id);
                    console.log('Data Filtering Status:', currentUser?.tenant_id === report?.tenant_id ? 'MATCH - User can access this report' : 'MISMATCH - Check access permissions');

                    // Test data transformation
                    const transformedData = transformReportDataForPDF(report);
                    console.log('Transformed Test Data:', transformedData);

                    console.log('=== END BILLING REPORT DEBUG INFO ===');
                    alert('Check browser console for detailed billing report PDF debug information');
                  }}
                  className="me-2"
                >
                  Debug Report Data
                </Button>
                <Button
                  variant="outline-secondary"
                  size="sm"
                  onClick={() => {
                    console.log('Testing PDF libraries...');
                    console.log('jsPDF available:', typeof jsPDF !== 'undefined');
                    console.log('QRCode available:', typeof QRCode !== 'undefined');
                    console.log('JsBarcode available:', typeof JsBarcode !== 'undefined');
                    console.log('autoTable available:', autoTableAvailable);
                    alert('Check browser console for PDF library status');
                  }}
                >
                  Test Libraries
                </Button>
              </Col>
            </Row>
          </Card.Body>
        </Card>
      )}

      {/* Edit Mode Information */}
      {editMode && (
        <Alert variant="info" className="mb-4">
          <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
          <strong>Edit Mode:</strong>
          <span className="ms-2">
            <Badge bg="secondary" className="me-2">
              <i className="fas fa-lock me-1"></i>Locked sections
            </Badge>
            contain existing data that cannot be modified.
          </span>
          <span className="ms-2">
            <Badge bg="success" className="me-2">
              <i className="fas fa-edit me-1"></i>Editable sections
            </Badge>
            allow you to add new sample and processing information.
          </span>
        </Alert>
      )}

      {/* Report Content - Exact replica of modal content */}
      <div>
        {/* Report Header */}
        <Row className="mb-4">
          <Col md={6}>
            <Card className={`border-left-primary h-100 ${editMode ? 'locked-section' : ''}`}>
              <Card.Body>
                <h6 className="text-primary mb-2">
                  Report Information
                  {editMode && (
                    <Badge bg="secondary" className="ms-2 small">
                      <i className="fas fa-lock me-1"></i>Locked
                    </Badge>
                  )}
                </h6>
                {/* Always show as read-only in edit mode - this data is locked */}
                <p className="mb-1"><strong>SID Number:</strong> {report.sid_number}</p>
                <p className="mb-1"><strong>Billing Date:</strong> {billingReportsAPI.formatDate(report.billing_date)}</p>
                <p className="mb-1"><strong>Generated:</strong> {billingReportsAPI.formatDateTime(report.generation_timestamp)}</p>
                <p className="mb-0"><strong>Status:</strong> <Badge bg={billingReportsAPI.getStatusVariant(report.metadata?.status)}>{report.metadata?.status}</Badge></p>
              </Card.Body>
            </Card>
          </Col>
          <Col md={6}>
            <Card className={`border-left-info h-100 ${editMode ? 'locked-section' : ''}`}>
              <Card.Body>
                <h6 className="text-info mb-2">
                  Clinic Information
                  {editMode && (
                    <Badge bg="secondary" className="ms-2 small">
                      <i className="fas fa-lock me-1"></i>Locked
                    </Badge>
                  )}
                </h6>
                {/* Always show as read-only in edit mode - this data is locked */}
                <p className="mb-1"><strong>Clinic:</strong> {report.clinic_info?.name}</p>
                <p className="mb-1"><strong>Site Code:</strong> {report.clinic_info?.site_code}</p>
                <p className="mb-1"><strong>Contact:</strong> {report.clinic_info?.contact_phone}</p>
                <p className="mb-0"><strong>Email:</strong> {report.clinic_info?.email}</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Patient Information - Locked in Edit Mode */}
        <div className={editMode ? 'locked-section' : ''}>
          <h6 className="text-primary mb-3">
            <FontAwesomeIcon icon={faUser} className="me-2" />
            Patient Information
            {editMode && (
              <Badge bg="secondary" className="ms-2 small">
                <i className="fas fa-lock me-1"></i>Locked
              </Badge>
            )}
          </h6>
          <Row className="mb-3">
            <Col md={6}>
              <p className="mb-1"><strong>Name:</strong> {report.patient_info?.full_name}</p>
              <p className="mb-1"><strong>Patient ID:</strong> {report.patient_info?.patient_id}</p>
              <p className="mb-1"><strong>Date of Birth:</strong> {billingReportsAPI.formatDate(report.patient_info?.date_of_birth)}</p>
            </Col>
            <Col md={6}>
              <p className="mb-1"><strong>Age/Gender:</strong> {report.patient_info?.age} / {report.patient_info?.gender}</p>
              <p className="mb-1"><strong>Blood Group:</strong> {report.patient_info?.blood_group || 'N/A'}</p>
              <p className="mb-1"><strong>Mobile:</strong> {report.patient_info?.mobile}</p>
            </Col>
          </Row>
          {report.patient_info?.email && (
            <Row className="mb-3">
              <Col md={12}>
                <p className="mb-1"><strong>Email:</strong> {report.patient_info?.email}</p>
              </Col>
            </Row>
          )}
        </div>

        {/* Billing Header - Locked in Edit Mode */}
        {report.billing_header && (
          <div className={editMode ? 'locked-section' : ''}>
            <h6 className="text-primary mb-3">
              <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
              Billing Information
              {editMode && (
                <Badge bg="secondary" className="ms-2 small">
                  <i className="fas fa-lock me-1"></i>Locked
                </Badge>
              )}
            </h6>
            <Row className="mb-3">
              <Col md={6}>
                <p className="mb-1"><strong>Invoice Number:</strong> {report.billing_header?.invoice_number}</p>
                <p className="mb-1"><strong>Referring Doctor:</strong> {report.billing_header?.referring_doctor}</p>
              </Col>
              <Col md={6}>
                <p className="mb-1"><strong>Payment Status:</strong> {report.billing_header?.payment_status}</p>
                <p className="mb-1"><strong>Payment Method:</strong> {report.billing_header?.payment_method}</p>
              </Col>
            </Row>
          </div>
        )}

        {/* Sample Information - Editable Section */}
        {editMode && (
          <div className="editable-section mb-4">
            <h6 className="text-success mb-3">
              <FontAwesomeIcon icon={faVial} className="me-2" />
              Sample Information
              <Badge bg="success" className="ms-2 small">
                <i className="fas fa-edit me-1"></i>Editable
              </Badge>
            </h6>
            <Row className="mb-3">
              <Col md={6}>
                <div className="mb-3">
                  <label className="form-label text-muted">
                    Sample ID
                    <Badge bg="info" className="ms-2 small">
                      <i className="fas fa-magic me-1"></i>Auto-generated
                    </Badge>
                  </label>
                  <Form.Control
                    type="text"
                    value={sampleData.sample_info?.sample_id || ''}
                    readOnly
                    className="bg-light"
                    placeholder="Auto-generated based on clinic and SID"
                  />
                </div>
                <div className="mb-3">
                  <label className="form-label text-muted">Sample Type</label>
                  <Form.Select
                    value={sampleData.sample_info?.sample_type || ''}
                    onChange={(e) => handleSampleDataChange('sample_info', 'sample_type', e.target.value)}
                  >
                    <option value="">Select Sample Type</option>
                    <option value="Blood">Blood</option>
                    <option value="Urine">Urine</option>
                    <option value="Stool">Stool</option>
                    <option value="Sputum">Sputum</option>
                    <option value="Serum">Serum</option>
                    <option value="Plasma">Plasma</option>
                    <option value="Other">Other</option>
                  </Form.Select>
                </div>
                <div className="mb-3">
                  <label className="form-label text-muted">Collection Date</label>
                  <Form.Control
                    type="datetime-local"
                    value={sampleData.sample_info?.collection_date || ''}
                    onChange={(e) => handleSampleDataChange('sample_info', 'collection_date', e.target.value)}
                  />
                </div>
                <div className="mb-3">
                  <label className="form-label text-muted">Collection Time</label>
                  <Form.Control
                    type="time"
                    value={sampleData.sample_info?.collection_time || ''}
                    onChange={(e) => handleSampleDataChange('sample_info', 'collection_time', e.target.value)}
                  />
                </div>
              </Col>
              <Col md={6}>
                <div className="mb-3">
                  <label className="form-label text-muted">Sample Status</label>
                  <Form.Select
                    value={sampleData.sample_info?.status || ''}
                    onChange={(e) => handleSampleDataChange('sample_info', 'status', e.target.value)}
                  >
                    <option value="">Select Status</option>
                    <option value="Collected">Collected</option>
                    <option value="In Transit">In Transit</option>
                    <option value="Received">Received</option>
                    <option value="Processing">Processing</option>
                    <option value="Completed">Completed</option>
                    <option value="Rejected">Rejected</option>
                  </Form.Select>
                </div>
                <div className="mb-3">
                  <label className="form-label text-muted">Container Type</label>
                  <Form.Select
                    value={sampleData.sample_info?.container_type || ''}
                    onChange={(e) => handleSampleDataChange('sample_info', 'container_type', e.target.value)}
                  >
                    <option value="">Select Container</option>
                    <option value="EDTA Tube">EDTA Tube</option>
                    <option value="Plain Tube">Plain Tube</option>
                    <option value="Fluoride Tube">Fluoride Tube</option>
                    <option value="Urine Container">Urine Container</option>
                    <option value="Stool Container">Stool Container</option>
                    <option value="Other">Other</option>
                  </Form.Select>
                </div>
                <div className="mb-3">
                  <label className="form-label text-muted">Volume (ml)</label>
                  <Form.Control
                    type="number"
                    step="0.1"
                    value={sampleData.sample_info?.volume || ''}
                    onChange={(e) => handleSampleDataChange('sample_info', 'volume', parseFloat(e.target.value) || 0)}
                    placeholder="Enter volume"
                  />
                </div>
                <div className="mb-3">
                  <label className="form-label text-muted">Priority</label>
                  <Form.Select
                    value={sampleData.sample_info?.priority || ''}
                    onChange={(e) => handleSampleDataChange('sample_info', 'priority', e.target.value)}
                  >
                    <option value="">Select Priority</option>
                    <option value="Normal">Normal</option>
                    <option value="Urgent">Urgent</option>
                    <option value="STAT">STAT</option>
                  </Form.Select>
                </div>
              </Col>
            </Row>
            <Row>
              <Col md={12}>
                <div className="mb-3">
                  <label className="form-label text-muted">Collection Notes</label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    value={sampleData.sample_info?.collection_notes || ''}
                    onChange={(e) => handleSampleDataChange('sample_info', 'collection_notes', e.target.value)}
                    placeholder="Enter any notes about sample collection..."
                  />
                </div>
              </Col>
            </Row>
          </div>
        )}

        {/* Processing Information - Editable Section */}
        {editMode && (
          <div className="editable-section mb-4">
            <h6 className="text-success mb-3">
              <FontAwesomeIcon icon={faSpinner} className="me-2" />
              Processing Information
              <Badge bg="success" className="ms-2 small">
                <i className="fas fa-edit me-1"></i>Editable
              </Badge>
            </h6>
            <Row className="mb-3">
              <Col md={6}>
                <div className="mb-3">
                  <label className="form-label text-muted">Received Date</label>
                  <Form.Control
                    type="datetime-local"
                    value={sampleData.processing_info?.received_date || ''}
                    onChange={(e) => handleSampleDataChange('processing_info', 'received_date', e.target.value)}
                  />
                </div>
                <div className="mb-3">
                  <label className="form-label text-muted">Processing Started</label>
                  <Form.Control
                    type="datetime-local"
                    value={sampleData.processing_info?.processing_started || ''}
                    onChange={(e) => handleSampleDataChange('processing_info', 'processing_started', e.target.value)}
                  />
                </div>
                <div className="mb-3">
                  <label className="form-label text-muted">Expected Completion</label>
                  <Form.Control
                    type="datetime-local"
                    value={sampleData.processing_info?.expected_completion || ''}
                    onChange={(e) => handleSampleDataChange('processing_info', 'expected_completion', e.target.value)}
                  />
                </div>
              </Col>
              <Col md={6}>
                <div className="mb-3">
                  <label className="form-label text-muted">Technician</label>
                  <Form.Control
                    type="text"
                    value={sampleData.processing_info?.technician || ''}
                    onChange={(e) => handleSampleDataChange('processing_info', 'technician', e.target.value)}
                    placeholder="Enter technician name"
                  />
                </div>
                <div className="mb-3">
                  <label className="form-label text-muted">Lab Section</label>
                  <Form.Select
                    value={sampleData.processing_info?.lab_section || ''}
                    onChange={(e) => handleSampleDataChange('processing_info', 'lab_section', e.target.value)}
                  >
                    <option value="">Select Lab Section</option>
                    <option value="Hematology">Hematology</option>
                    <option value="Biochemistry">Biochemistry</option>
                    <option value="Microbiology">Microbiology</option>
                    <option value="Immunology">Immunology</option>
                    <option value="Pathology">Pathology</option>
                    <option value="Molecular Biology">Molecular Biology</option>
                  </Form.Select>
                </div>
                <div className="mb-3">
                  <label className="form-label text-muted">Quality Check</label>
                  <Form.Select
                    value={sampleData.processing_info?.quality_check || ''}
                    onChange={(e) => handleSampleDataChange('processing_info', 'quality_check', e.target.value)}
                  >
                    <option value="">Select Quality Status</option>
                    <option value="Passed">Passed</option>
                    <option value="Failed">Failed</option>
                    <option value="Pending">Pending</option>
                    <option value="Retest Required">Retest Required</option>
                  </Form.Select>
                </div>
              </Col>
            </Row>
            <Row>
              <Col md={12}>
                <div className="mb-3">
                  <label className="form-label text-muted">Processing Notes</label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    value={sampleData.processing_info?.processing_notes || ''}
                    onChange={(e) => handleSampleDataChange('processing_info', 'processing_notes', e.target.value)}
                    placeholder="Enter any notes about sample processing..."
                  />
                </div>
              </Col>
            </Row>
          </div>
        )}

        {/* Test Items - Enhanced Card Layout */}
        <PaginatedTestCards
          testItems={report.test_items || []}
          title="Test Details"
          itemsPerPage={5}
        />

        {/* Unmatched Tests Warning */}
        {report.unmatched_tests && report.unmatched_tests.length > 0 && (
          <Alert variant="warning" className="mb-3">
            <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
            <strong>Unmatched Tests ({report.unmatched_tests.length}):</strong>
            <ul className="mb-0 mt-2">
              {report.unmatched_tests.map((test, index) => (
                <li key={index}>{test}</li>
              ))}
            </ul>
          </Alert>
        )}

        {/* Financial Summary - Locked in Edit Mode */}
        {report.financial_summary && (
          <div className={editMode ? 'locked-section' : ''}>
            <h6 className="text-primary mb-3">
              <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
              Financial Summary
              {editMode && (
                <Badge bg="secondary" className="ms-2 small">
                  <i className="fas fa-lock me-1"></i>Locked
                </Badge>
              )}
            </h6>
            <Row>
              <Col md={6}>
                <p className="mb-1"><strong>Bill Amount:</strong> {billingReportsAPI.formatCurrency(report.financial_summary.bill_amount)}</p>
                <p className="mb-1"><strong>Other Charges:</strong> {billingReportsAPI.formatCurrency(report.financial_summary.other_charges)}</p>
                <p className="mb-1"><strong>Discount ({report.financial_summary.discount_percent}%):</strong> {billingReportsAPI.formatCurrency(report.financial_summary.discount_amount)}</p>
                <p className="mb-1"><strong>Subtotal:</strong> {billingReportsAPI.formatCurrency(report.financial_summary.subtotal)}</p>
              </Col>
              <Col md={6}>
                <p className="mb-1"><strong>GST ({report.financial_summary.gst_rate}%):</strong> {billingReportsAPI.formatCurrency(report.financial_summary.gst_amount)}</p>
                <p className="mb-1"><strong>Total Amount:</strong> <span className="text-success fw-bold">{billingReportsAPI.formatCurrency(report.financial_summary.total_amount)}</span></p>
                <p className="mb-1"><strong>Paid Amount:</strong> {billingReportsAPI.formatCurrency(report.financial_summary.paid_amount)}</p>
                <p className="mb-0"><strong>Balance:</strong> <span className={report.financial_summary.balance > 0 ? 'text-danger fw-bold' : 'text-success'}>{billingReportsAPI.formatCurrency(report.financial_summary.balance)}</span></p>
              </Col>
            </Row>
          </div>
        )}

        {/* Report Metadata */}
        {report.metadata && (
          <>
            <h6 className="text-primary mb-3 mt-4">Report Metadata</h6>
            <Row>
              <Col md={6}>
                <p className="mb-1"><strong>Test Match Rate:</strong>
                  <span className={billingReportsAPI.getMatchRateColor(report.metadata.test_match_success_rate)}>
                    {' '}{Math.round(report.metadata.test_match_success_rate * 100)}%
                  </span>
                </p>
                <p className="mb-1"><strong>Total Tests:</strong> {report.metadata.total_tests}</p>
              </Col>
              <Col md={6}>
                <p className="mb-1"><strong>Matched Tests:</strong> {report.metadata.matched_tests_count}</p>
                <p className="mb-1"><strong>Unmatched Tests:</strong> {report.metadata.unmatched_tests_count}</p>
              </Col>
            </Row>
          </>
        )}
      </div>
    </div>
  );
};

export default BillingReportsDetail;
