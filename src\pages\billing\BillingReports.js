import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Card, Form, Button, Row, Col, Table, InputGroup, Alert,
  Badge, Spinner, Modal, OverlayTrigger, Tooltip, Dropdown
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch, faDownload, faEye, faFileInvoiceDollar,
  faCalendarAlt, faUser, faPhone, faBuilding, faSpinner,
  faExclamationTriangle, faCheckCircle, faInfoCircle, faFilter
} from '@fortawesome/free-solid-svg-icons';
import billingReportsAPI from '../../services/billingReportsAPI';
import { useAuth } from '../../context/AuthContext';
import { useTenant } from '../../context/TenantContext';
import PaginatedTestCards from '../../components/billing/PaginatedTestCards';
import Pagination from '../../components/common/Pagination';
import '../../styles/BillingReports.css';
import '../../styles/TestDetailsCard.css';

// Enhanced PDF generation imports - FIXED: Proper ES6 imports
import jsPDF from 'jspdf';
import QRCode from 'qrcode';
import JsBarcode from 'jsbarcode';

// Try to import autoTable plugin, fallback if not available
let autoTableAvailable = false;
try {
  require('jspdf-autotable');
  autoTableAvailable = true;
} catch (e) {
  console.warn('jspdf-autotable not available, using fallback table generation');
}

const BillingReports = () => {
  // const location = useLocation();
  const navigate = useNavigate();

  const { user } = useAuth();
  const { accessibleTenants, currentTenantContext } = useTenant();

  // State management
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [error, setError] = useState('');
  const [stats, setStats] = useState(null);

  // Franchise filtering state
  const [selectedFranchiseId, setSelectedFranchiseId] = useState(null);
  const [allReports, setAllReports] = useState([]); // Store all reports for filtering

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Debug logging
  console.log('BillingReports render - reports:', reports.length, 'loading:', loading);

  // Search state
  const [searchParams, setSearchParams] = useState({
    sid: '',
    patient_name: '',
    mobile: '',
    date_from: '',
    date_to: ''
  });

  // SID autocomplete state
  const [sidSuggestions, setSidSuggestions] = useState([]);
  const [showSidSuggestions, setShowSidSuggestions] = useState(false);

  // Modal state
  const [showReportModal, setShowReportModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [reportModalLoading, setReportModalLoading] = useState(false);
  const [downloadingPDF, setDownloadingPDF] = useState(false);

  // Enhanced PDF generation state
  const [includeHeader, setIncludeHeader] = useState(true);

  // LOGO INTEGRATION: State for logo base64 data
  const [logoBase64, setLogoBase64] = useState(null);

  // Check if PDF libraries are available - FIXED: Use imported libraries
  const pdfLibrariesAvailable = !!(jsPDF && QRCode && JsBarcode);

  // Helper function to check if user can access all franchises
  const canAccessAllFranchises = () => {
    // Admin role can access all franchises
    if (user?.role === 'admin') return true;

    // Users from Mayiladuthurai hub (tenant_id 1) can access all franchises
    if (currentTenantContext?.is_hub && currentTenantContext?.site_code === 'MYD') return true;

    return false;
  };

  // Helper function to get available franchises for filtering
  const getAvailableFranchises = () => {
    if (!canAccessAllFranchises()) return [];
    return accessibleTenants || [];
  };

  // Helper function to filter reports by franchise
  const filterReportsByFranchise = (reportsData) => {
    if (!canAccessAllFranchises() || !selectedFranchiseId) {
      return reportsData;
    }
    return reportsData.filter(report => report.tenant_id === selectedFranchiseId);
  };



  // SIMPLIFIED PNG LOGO LOADER: Direct PNG loading without AVIF conversion
  const convertLogoToBase64 = async () => {
    console.log('=== PNG LOGO LOADING START ===');

    try {
      // Create canvas for PNG to base64 conversion
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      // Enable CORS for cross-origin images
      img.crossOrigin = 'anonymous';

      return new Promise((resolve) => {
        img.onload = () => {
          try {
            console.log('=== PNG LOGO LOADED SUCCESSFULLY ===');
            console.log('Logo image properties:', {
              src: img.src,
              width: img.width,
              height: img.height,
              naturalWidth: img.naturalWidth,
              naturalHeight: img.naturalHeight,
              complete: img.complete
            });

            // Validate image dimensions
            if (img.width === 0 || img.height === 0) {
              console.error('Invalid PNG logo dimensions:', img.width, 'x', img.height);
              resolve(null);
              return;
            }

            // Set canvas size to maintain aspect ratio
            canvas.width = img.naturalWidth || img.width;
            canvas.height = img.naturalHeight || img.height;
            console.log('Canvas dimensions set to:', canvas.width, 'x', canvas.height);

            // Clear canvas and draw PNG image
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);
            console.log('PNG image drawn to canvas successfully');

            // Convert to base64 PNG (no format conversion needed)
            const base64 = canvas.toDataURL('image/png');
            console.log('=== PNG BASE64 CONVERSION SUCCESSFUL ===');
            console.log('Base64 data properties:', {
              length: base64.length,
              startsWithDataUrl: base64.startsWith('data:image/png;base64,'),
              preview: base64.substring(0, 50) + '...',
              isValid: base64.length > 100 && base64.includes('data:image/png;base64,')
            });

            // Validate base64 data
            if (!base64 || base64.length < 100 || !base64.startsWith('data:image/png;base64,')) {
              console.error('Invalid PNG base64 data generated');
              resolve(null);
              return;
            }

            console.log('=== PNG LOGO CONVERSION COMPLETED SUCCESSFULLY ===');
            resolve(base64);

          } catch (conversionErr) {
            console.error('=== ERROR DURING PNG LOGO CONVERSION ===');
            console.error('PNG conversion error:', conversionErr);
            console.error('Canvas state:', {
              width: canvas.width,
              height: canvas.height,
              context: !!ctx
            });
            resolve(null);
          }
        };

        img.onerror = (err) => {
          console.error('=== PNG LOGO LOAD FAILED ===');
          console.error('PNG load error:', err);
          console.error('Error details:', {
            src: img.src,
            currentSrc: img.currentSrc,
            complete: img.complete,
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight
          });
          resolve(null);
        };

        // LOGO FILE CONFIGURATION: Load PNG logo from public directory
        const logoPath = '/logoavini.png';
        console.log('=== ATTEMPTING TO LOAD PNG LOGO ===');
        console.log('PNG logo path:', logoPath);
        console.log('Full URL:', window.location.origin + logoPath);
        console.log('Current location:', window.location.href);

        img.src = logoPath;

        // Add timeout to detect hanging loads
        setTimeout(() => {
          if (!img.complete) {
            console.warn('PNG logo loading timeout - image not loaded after 5 seconds');
            console.log('PNG image state:', {
              src: img.src,
              complete: img.complete,
              naturalWidth: img.naturalWidth,
              naturalHeight: img.naturalHeight
            });
          }
        }, 5000);
      });

    } catch (err) {
      console.error('=== PNG LOGO FUNCTION ERROR ===');
      console.error('Function error:', err);
      console.log('=== PNG LOGO LOADING END ===');
      return null;
    }
  };

  // Load initial data and logo
  useEffect(() => {
    loadReports();
    loadStats();

    // VERIFY PNG FILE ACCESS: Test if PNG logo file is accessible
    const testLogoAccess = async () => {
      try {
        console.log('=== TESTING PNG LOGO FILE ACCESS ===');
        const response = await fetch('/logoavini.png');
        console.log('PNG logo file fetch response:', {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok,
          url: response.url,
          type: response.type,
          headers: Object.fromEntries(response.headers.entries())
        });

        if (response.ok) {
          const blob = await response.blob();
          console.log('PNG logo file blob:', {
            size: blob.size,
            type: blob.type
          });
          console.log('=== PNG LOGO FILE ACCESS SUCCESSFUL ===');
        } else {
          console.error('PNG logo file not accessible:', response.status, response.statusText);
        }
      } catch (fetchErr) {
        console.error('=== PNG LOGO FILE ACCESS FAILED ===');
        console.error('Fetch error:', fetchErr);
      }
    };

    // Test PNG logo file access first
    testLogoAccess();

    // PNG LOGO LOADING: Direct PNG loading and conversion
    console.log('=== PNG LOGO INITIALIZATION ===');
    convertLogoToBase64().then(base64 => {
      if (base64) {
        setLogoBase64(base64);
        console.log('PNG logo converted to base64 successfully');
        console.log('Logo state updated - base64 length:', base64.length);
        console.log('Logo data starts with:', base64.substring(0, 30));
        console.log('=== PNG LOGO READY FOR PDF GENERATION ===');
      } else {
        console.error('PNG logo conversion failed - base64 is null');
        console.error('Check if logoavini.png exists in public directory');
      }
      console.log('=== END PNG LOGO INITIALIZATION ===');
    }).catch(err => {
      console.error('PNG logo conversion promise rejected:', err);
    });
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Reload data when franchise selection changes
  useEffect(() => {
    if (canAccessAllFranchises()) {
      loadReports();
      loadStats();
    }
  }, [selectedFranchiseId]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadReports = async () => {
    setLoading(true);
    setError('');

    try {
      console.log('[BillingReports] Starting to load all reports...');
      console.log('[BillingReports] Current user:', user);
      console.log('[BillingReports] Selected franchise:', selectedFranchiseId);

      // Use getAllReports to get all reports with role-based filtering
      const response = await billingReportsAPI.getAllReports(selectedFranchiseId);

      if (response.success) {
        // Extract reports data from the nested response structure
        let reportsData = response.data?.data?.data || response.data?.data || [];

        // Ensure we have an array of reports
        if (Array.isArray(reportsData)) {
          console.log('[BillingReports] Loaded reports:', reportsData.length);

          // Store all reports for filtering
          setAllReports(reportsData);

          // Apply franchise filtering if applicable
          const filteredReports = filterReportsByFranchise(reportsData);
          setReports(filteredReports);

          // Reset to first page when data changes
          setCurrentPage(1);
        } else {
          console.error('Reports data is not an array:', reportsData);
          setReports([]);
          setAllReports([]);
        }
      } else {
        setError(response.error || 'Failed to load reports');
        setReports([]);
        setAllReports([]);
      }
    } catch (err) {
      console.error('[BillingReports] Load reports error:', err);
      console.error('[BillingReports] Error details:', err.response?.data);
      setError('Failed to load billing reports: ' + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      console.log('[BillingReports] Loading stats for franchise:', selectedFranchiseId);
      const response = await billingReportsAPI.getReportsStats(selectedFranchiseId);
      console.log('[BillingReports] Stats response:', response);

      if (response.success) {
        // The API returns: { success: true, data: { data: { data: actualStats } } }
        const statsData = response.data?.data?.data || response.data?.data || response.data;
        console.log('[BillingReports] Extracted stats data:', statsData);
        setStats(statsData || {});
      } else {
        console.error('[BillingReports] Stats API error:', response.error);
      }
    } catch (err) {
      console.error('Failed to load stats:', err);
    }
  };

  const handleSearch = async () => {
    setSearchLoading(true);
    setError('');

    try {
      const response = await billingReportsAPI.searchReports(searchParams, selectedFranchiseId);
      if (response.success) {
        let reportsData = response.data?.data?.data || response.data?.data || [];
        console.log('[BillingReports] Search results:', reportsData.length);

        if (Array.isArray(reportsData)) {
          // Store all search results
          setAllReports(reportsData);

          // Apply franchise filtering if applicable
          const filteredReports = filterReportsByFranchise(reportsData);
          setReports(filteredReports);

          // Reset to first page when search results change
          setCurrentPage(1);
        } else {
          setReports([]);
          setAllReports([]);
        }
      } else {
        setError(response.error);
      }
    } catch (err) {
      console.error('[BillingReports] Search error:', err);
      setError('Failed to search billing reports');
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSIDChange = async (value) => {
    setSearchParams(prev => ({ ...prev, sid: value }));
    
    if (value.length >= 2) {
      try {
        const response = await billingReportsAPI.getSIDAutocomplete(value);
        if (response.success) {
          const suggestionsData = response.data?.data || response.data || [];
          setSidSuggestions(Array.isArray(suggestionsData) ? suggestionsData : []);
          setShowSidSuggestions(true);
        }
      } catch (err) {
        console.error('Failed to get SID suggestions:', err);
      }
    } else {
      setSidSuggestions([]);
      setShowSidSuggestions(false);
    }
  };

  const handleSIDSelect = (sid) => {
    setSearchParams(prev => ({ ...prev, sid }));
    setShowSidSuggestions(false);
    setSidSuggestions([]);
  };

  // Navigate to billing reports detail page
  const handleNavigateToDetail = (report, editMode = false) => {
    const params = new URLSearchParams();
    if (editMode) {
      params.append('edit', 'true');
    }

    const queryString = params.toString();
    const url = `/billing/reports/${report.sid_number}${queryString ? `?${queryString}` : ''}`;

    navigate(url, {
      state: { from: 'billing-reports' }
    });
  };

  const handleViewReport = async (report) => {
    setSelectedReport(report);
    setShowReportModal(true);
    setReportModalLoading(true);

    try {
      const response = await billingReportsAPI.getReportBySID(report.sid_number);

      if (response.success && response.data) {
        // The API returns: { success: true, data: { data: { data: actualReport } } }
        // So we need to access response.data.data.data
        const reportData = response.data.data?.data || response.data.data || response.data;

        if (reportData && typeof reportData === 'object') {
          // Force a state update by creating a new object
          setSelectedReport({ ...reportData });
        } else {
          setError('Invalid report data structure received');
        }
      } else {
        setError(response.error || 'Failed to load report details');
      }
    } catch (err) {
      console.error('Error loading report:', err);
      setError('Failed to load report details');
    } finally {
      setReportModalLoading(false);
    }
  };

  // ENHANCED PDF generation with optimized formatting
  const handleDownloadPDF = async (report) => {
    if (!report) return;

    try {
      setDownloadingPDF(true);
      setError(null);

      // VALIDATE LOGO STATE: Check logo state before PDF generation
      console.log('=== LOGO STATE VALIDATION ===');
      console.log('Logo base64 state:', {
        exists: !!logoBase64,
        type: typeof logoBase64,
        length: logoBase64 ? logoBase64.length : 0,
        startsWithDataUrl: logoBase64 ? logoBase64.startsWith('data:image/') : false,
        includeHeader: includeHeader
      });

      if (logoBase64) {
        console.log('Logo state is valid for PDF generation');
      } else {
        console.warn('Logo state is null - logo will not appear in PDF');
      }
      console.log('=== END LOGO STATE VALIDATION ===');

      // Log enhanced PDF generation context
      console.log('=== ENHANCED PDF GENERATION - BILLING REPORTS ===');
      console.log('Current User:', user);
      console.log('Current Tenant:', currentTenantContext);
      console.log('Report Data:', report);
      console.log('Report SID:', report.sid_number);
      console.log('Enhanced Features:');
      console.log('- Compact spacing optimization: ENABLED');
      console.log('- Separate reference range display: ENABLED');
      console.log('- Dynamic signatures: ENABLED');
      console.log('- Fixed page numbering: ENABLED');
      console.log('- Logo integration:', !!logoBase64 ? 'ENABLED' : 'DISABLED (no logo data)');
      console.log('=== END CONTEXT ===');

      console.log('Starting enhanced PDF generation for report:', report.sid_number);

      // FIXED: Check if PDF libraries are available using imported modules
      if (!jsPDF) {
        throw new Error('jsPDF library not available. Please refresh the page and try again.');
      }
      if (!QRCode) {
        console.warn('QRCode library not available. QR codes will be disabled.');
      }
      if (!JsBarcode) {
        console.warn('JsBarcode library not available. Barcodes will be disabled.');
      }

      console.log('PDF Libraries Status:');
      console.log('- jsPDF available:', !!jsPDF);
      console.log('- QRCode available:', !!QRCode);
      console.log('- JsBarcode available:', !!JsBarcode);
      console.log('- autoTable available:', autoTableAvailable);

      // Get full report data if needed
      let fullReportData = report;
      if (!report.test_items && !report.billing_items) {
        console.log('Fetching full report data for PDF generation...');
        const response = await billingReportsAPI.getReportBySID(report.sid_number);
        if (response.success && response.data) {
          fullReportData = response.data.data?.data || response.data.data || response.data;
        }
      }

      // Generate enhanced PDF
      await generateEnhancedPDF(fullReportData);

    } catch (err) {
      console.error('Error generating enhanced PDF:', err);
      setError('Failed to generate PDF: ' + err.message);
    } finally {
      setDownloadingPDF(false);
    }
  };

  // ENHANCED PDF generation function with optimized formatting
  const generateEnhancedPDF = async (report) => {
    try {
      // Create new jsPDF instance
      const doc = new jsPDF('p', 'mm', 'A4');
      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();

      let yPosition = 20;

      // Generate QR code with direct PDF download URL
      const qrCodeData = `${window.location.origin}/api/billing-reports/sid/${report.sid_number}/pdf`;
      console.log('Generating QR code for:', qrCodeData);

      // FIXED: Generate QR code using imported QRCode module
      let qrCodeImg = null;
      try {
        if (QRCode) {
          const canvas = document.createElement('canvas');
          await QRCode.toCanvas(canvas, qrCodeData, { width: 100, margin: 1 });
          qrCodeImg = canvas.toDataURL('image/png');
          console.log('QR code generated successfully');
        } else {
          console.warn('QRCode library not available');
        }
      } catch (qrErr) {
        console.warn('QR code generation failed:', qrErr);
      }

      // FIXED: Generate barcode using imported JsBarcode module
      let barcodeImg = null;
      try {
        if (JsBarcode) {
          const canvas = document.createElement('canvas');
          JsBarcode(canvas, report.sid_number, {
            format: 'CODE128',
            width: 1,
            height: 40,
            displayValue: true,
            fontSize: 12
          });
          barcodeImg = canvas.toDataURL('image/png');
          console.log('Barcode generated successfully');
        } else {
          console.warn('JsBarcode library not available');
        }
      } catch (barcodeErr) {
        console.warn('Barcode generation failed:', barcodeErr);
      }

      // TEST DATA DETECTION: Check if report has test data content for conditional rendering
      const hasTestContent = report.test_items && Array.isArray(report.test_items) && report.test_items.length > 0;

      console.log('PDF Generation - Test data detection:', {
        hasTestItems: !!report.test_items,
        isArray: Array.isArray(report.test_items),
        itemCount: report.test_items ? report.test_items.length : 0,
        hasTestContent: hasTestContent
      });

      // Add header if enabled and has content (suppress header for content-less pages)
      if (includeHeader && hasTestContent) {
        yPosition = generatePDFHeader(doc, pageWidth, yPosition);
        console.log('Header added - test content detected');
      } else if (!includeHeader) {
        // When no header, adjust top margin to start content higher
        yPosition = 15; // Reduced top margin for no-header layout
        console.log('No header mode - reduced top margin');
      } else {
        console.log('Header suppressed - no test content detected');
      }

      // Add patient and report information
      yPosition = generatePatientReportSection(doc, report, yPosition, pageWidth, barcodeImg);

      // Generate test results table with enhanced formatting and conditional headers
      const testResults = generateTestResultsTable(doc, report, yPosition, pageWidth, pageHeight, includeHeader, hasTestContent);
      yPosition = testResults.yPosition;

      // Generate QR code and signature section
      await generateQRCodeAndSignatureSection(doc, qrCodeImg, pageWidth, pageHeight, yPosition);

      // Save the PDF with patient-specific filename
      const patientInfo = report.patient_info || {};
      const patientName = (patientInfo.full_name ||
                          `${patientInfo.first_name || ''} ${patientInfo.last_name || ''}`.trim() ||
                          'Patient').replace(/[^a-zA-Z0-9]/g, '_'); // Clean filename

      const sidNumber = report.sid_number || report.sample_id || 'Report';
      const timestamp = new Date().toISOString().slice(0, 10);
      const filename = `${patientName}_${sidNumber}_${timestamp}.pdf`;

      console.log('Saving enhanced PDF:', filename);
      doc.save(filename);

    } catch (err) {
      console.error('Error in generateEnhancedPDF:', err);
      throw err;
    }
  };

  // ENHANCED: PDF Header Generation with clean layout
  const generatePDFHeader = (doc, pageWidth, yPos) => {
    // Pink header bar only (no logo inside)
    doc.setFillColor(236, 72, 153);
    doc.rect(0, 0, pageWidth, 8, 'F');

    // HEADER TEXT CLEANUP: Remove "INVESTIGATION REPORT" title for cleaner layout
    // (Title removed as requested for professional appearance)

    // Return with adequate spacing below header
    return yPos + 10; // Reduced spacing since no title text
  };

  // FIXED: Enhanced Patient and Report Information Section with proper layout
  const generatePatientReportSection = (doc, report, yPos, pageWidth, barcodeImg) => {
    let yPosition = yPos;

    try {
      console.log('Generating patient section with report data:', report);

      // FIXED: Proper margins (20mm = ~72pt, but using 20pt for better fit)
      const leftMargin = 20;
      const rightMargin = pageWidth - 20;

      // CRITICAL FIX: Barcode positioning with conditional logic for header mode
      if (barcodeImg) {
        if (includeHeader) {
          // With header: position barcode higher and further right to avoid overlap
          doc.addImage(barcodeImg, 'PNG', rightMargin - 45, yPosition + 0, 40, 10);
        } else {
          // BARCODE CONDITIONAL LOGIC: Without header, position barcode lower for reduced top margin
          doc.addImage(barcodeImg, 'PNG', rightMargin - 45, yPosition + 10, 40, 10);
        }
      }

      // Add extra spacing to prevent barcode overlap with patient info
      yPosition += 5;

      // LOGO INTEGRATION: Add logo to first page only in content area
      const isFirstPage = doc.internal.getCurrentPageInfo().pageNumber === 1;
      if (logoBase64 && includeHeader && isFirstPage) {
        try {
          // LOGO DEBUGGING: Verify logo data and positioning
          console.log('=== LOGO INTEGRATION DEBUG ===');
          console.log('Logo base64 data available:', !!logoBase64);
          console.log('Logo data length:', logoBase64 ? logoBase64.length : 0);
          console.log('Logo data sample:', logoBase64 ? logoBase64.substring(0, 50) + '...' : 'null');
          console.log('Include header state:', includeHeader);
          console.log('Current page number:', doc.internal.getCurrentPageInfo().pageNumber);
          console.log('Is first page:', isFirstPage);

          // LOGO SIZE ENHANCEMENT: Increased logo size while maintaining positioning
          const logoHeight = 30; // UPDATED: Increased from 15pt to 25pt height
          const logoWidth = logoHeight * 2.5; // Calculate width maintaining 1.5:1 aspect ratio (37.5pt width)
          const logoX = 15; // 15pt from left margin in content area (unchanged)
          const logoY = 10; // 25pt from top (below the 8pt pink header bar) in white content area (unchanged)

          console.log('Enhanced logo positioning coordinates:', { logoX, logoY, logoWidth, logoHeight });
          console.log('Logo size: 25pt height x 37.5pt width (1.5:1 aspect ratio)');

          // jsPDF INTEGRATION: Embed logo using addImage with error handling
          console.log('=== ATTEMPTING jsPDF addImage ===');
          console.log('addImage parameters:', {
            format: 'PNG',
            x: logoX,
            y: logoY,
            width: logoWidth,
            height: logoHeight,
            base64Length: logoBase64.length,
            base64Valid: logoBase64.startsWith('data:image/png;base64,')
          });

          doc.addImage(logoBase64, 'PNG', logoX, logoY, logoWidth, logoHeight);
          console.log('=== jsPDF addImage SUCCESSFUL ===');
          console.log('Enhanced logo added to PDF first page successfully at coordinates (15, 25)');

          // CONTENT SPACING: Updated content adjustment for enlarged logo with 5pt spacing
          yPosition = Math.max(yPosition, logoY + logoHeight + 5); // 25 + 25 + 5 = 55
          console.log('Content yPosition adjusted to:', yPosition, '(accounting for 25pt logo height + 5pt spacing)');
          console.log('=== END LOGO INTEGRATION DEBUG ===');

        } catch (logoErr) {
          console.error('=== LOGO INTEGRATION ERROR ===');
          console.error('Failed to add logo to PDF content area:', logoErr);
          console.error('Logo base64 validation:', {
            exists: !!logoBase64,
            type: typeof logoBase64,
            length: logoBase64 ? logoBase64.length : 0,
            startsWithDataUrl: logoBase64 ? logoBase64.startsWith('data:image/') : false
          });
          console.error('=== END LOGO ERROR ===');
        }
      } else {
        console.log('Logo skipped - Conditions:', {
          logoBase64Available: !!logoBase64,
          includeHeader: includeHeader,
          isFirstPage: isFirstPage,
          currentPage: doc.internal.getCurrentPageInfo().pageNumber
        });
      }

      // FIXED: Typography standards - 11pt for table data
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(11);
      doc.setFont('helvetica', 'normal');

      // FIXED: Two-column layout with proper positioning
      const leftColumnX = leftMargin;
      const leftColonX = leftMargin + 35;
      const leftValueX = leftMargin + 40;

      const rightColumnX = pageWidth / 2 + 10;
      const rightColonX = rightColumnX + 35;
      const rightValueX = rightColumnX + 40;

      // Extract patient data from report
      const patientInfo = report.patient_info || {};
      const patientName = patientInfo.full_name ||
                         patientInfo.name ||
                         `${patientInfo.first_name || ''} ${patientInfo.last_name || ''}`.trim() ||
                         'N/A';

      const patientAge = patientInfo.age || 'N/A';
      const patientGender = patientInfo.gender || 'N/A';

      // Extract clinic information
      const clinicInfo = report.clinic_info || {};
      const branchName = clinicInfo.name ||
                        clinicInfo.branch_name ||
                        currentTenantContext?.name ||
                        'AVINI LABS';

      // FIXED: Left column with proper spacing (using lineHeight = 6)
      doc.text('Patient', leftColumnX, yPosition);
      doc.text(':', leftColonX, yPosition);
      doc.text(patientName, leftValueX, yPosition);

      doc.text('Age/Sex', leftColumnX, yPosition + 6);
      doc.text(':', leftColonX, yPosition + 6);
      doc.text(`${patientAge}/${patientGender}`, leftValueX, yPosition + 6);

      doc.text('Referrer', leftColumnX, yPosition + 12);
      doc.text(':', leftColonX, yPosition + 12);
      doc.text(report.patient_info?.referred_by || 'Self', leftValueX, yPosition + 12);

      doc.text('Branch', leftColumnX, yPosition + 18);
      doc.text(':', leftColonX, yPosition + 18);
      doc.text(branchName, leftValueX, yPosition + 18);

      // FIXED: Right column with proper positioning and date formatting
      // Extract dates from report
      const sampleId = report.sid_number || report.sample_id || report.id || 'N/A';
      const regDate = report.registration_date ||
                     report.billing_date ||
                     report.invoice_date ||
                     report.created_at ||
                     new Date().toISOString();

      const collDate = report.collection_date ||
                      report.sample_collection_date ||
                      report.created_at ||
                      new Date().toISOString();

      const reportDate = report.reported_date ||
                        report.report_date ||
                        report.updated_at ||
                        new Date().toISOString();

      // FIXED: Compact date formatting for better fit
      const formatDate = (dateStr) => {
        try {
          const date = new Date(dateStr);
          return date.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit'
          }) + ' ' + date.toLocaleTimeString('en-GB', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
          });
        } catch {
          return 'N/A';
        }
      };

      // Right column data with consistent spacing
      doc.text('SID No.', rightColumnX, yPosition);
      doc.text(':', rightColonX, yPosition);
      doc.text(sampleId.toString(), rightValueX, yPosition);

      doc.text('Reg Date & Time', rightColumnX, yPosition + 6);
      doc.text(':', rightColonX, yPosition + 6);
      doc.text(formatDate(regDate), rightValueX, yPosition + 6);

      doc.text('Coll Date & Time', rightColumnX, yPosition + 12);
      doc.text(':', rightColonX, yPosition + 12);
      doc.text(formatDate(collDate), rightValueX, yPosition + 12);

      doc.text('Report Date & Time', rightColumnX, yPosition + 18);
      doc.text(':', rightColonX, yPosition + 18);
      doc.text(formatDate(reportDate), rightValueX, yPosition + 18);

    } catch (err) {
      console.error('Error in generatePatientReportSection:', err);
    }

    // FIXED: Return with proper spacing for next section
    return yPosition + 30;
  };

  // ENHANCED: Test Results Table with conditional header logic
  const generateTestResultsTable = (doc, reportData, yPos, pageWidth, pageHeight, includeHeader, hasTestContent = true) => {
    let yPosition = yPos;
    let pageCount = 1;
    let actualPageCount = 1;

    try {
      // FIXED: Proper page boundaries with margins (20pt top/bottom)
      const topMargin = 20;
      const bottomMargin = 20;
      const maxContentHeight = pageHeight - bottomMargin - 40; // Extra space for footer

      // Transform actual report data for PDF generation
      console.log('Using actual report data for PDF generation:', reportData);
      const actualTestData = transformReportDataForPDF(reportData);
      console.log('Transformed test data:', actualTestData);

      // ENHANCED TEST DATA DETECTION: Comprehensive validation for header display
      const hasActualTestData = reportData.test_items &&
                               Array.isArray(reportData.test_items) &&
                               reportData.test_items.length > 0;

      // ADDITIONAL VALIDATION: Check if transformed data contains actual tests
      const hasTransformedTestData = actualTestData &&
                                   Array.isArray(actualTestData) &&
                                   actualTestData.length > 0 &&
                                   actualTestData.some(category =>
                                     category.tests &&
                                     Array.isArray(category.tests) &&
                                     category.tests.length > 0
                                   );

      // FINAL TEST DATA DETERMINATION: Both original and transformed data must be valid
      const shouldDisplayHeaders = hasActualTestData && hasTransformedTestData;

      console.log('Enhanced test data detection:', {
        hasTestItems: !!reportData.test_items,
        isArray: Array.isArray(reportData.test_items),
        itemCount: reportData.test_items ? reportData.test_items.length : 0,
        hasActualTestData: hasActualTestData,
        hasTransformedTestData: hasTransformedTestData,
        shouldDisplayHeaders: shouldDisplayHeaders,
        transformedDataStructure: actualTestData ? actualTestData.map(cat => ({
          category: cat.category,
          testCount: cat.tests ? cat.tests.length : 0
        })) : 'No transformed data'
      });

      const dynamicTests = actualTestData;

      // CONDITIONAL SECTION TITLE AND HEADERS: Only show when valid test data exists
      if (shouldDisplayHeaders) {
        // Add section title with professional header formatting
        yPosition += 10; // Space before section

        // Add horizontal line above "INVESTIGATION REPORT" - full page width with uniform formatting
        doc.setDrawColor(0, 0, 0);
        doc.setLineWidth(1.2);
        doc.line(10, yPosition, pageWidth - 10, yPosition);
        yPosition += 5; // Reduced space after top line

        doc.setFont('helvetica', 'bold');
        doc.setFontSize(13);
        doc.setTextColor(0, 0, 0);
        doc.text('INVESTIGATION REPORT', pageWidth / 2, yPosition, { align: 'center' });
        yPosition += 5; // Reduced space after title

        // Add horizontal line below "INVESTIGATION REPORT" - full page width with uniform formatting
        doc.setDrawColor(0, 0, 0);
        doc.setLineWidth(1.2);
        doc.line(10, yPosition, pageWidth - 10, yPosition);
        yPosition += 10; // Space after bottom line

        // CONDITIONAL COLUMN HEADERS: Only add when test data exists
        yPosition = addColumnHeaders(doc, yPosition);
        console.log('Section title and column headers added - valid test data detected');
      } else {
        console.log('Section title and column headers suppressed - no valid test data detected');
        yPosition += 5; // Minimal spacing when no test section
      }

      // CRITICAL FIX: Enhanced page break function to prevent overlaps
      const checkPageBreak = (currentY, requiredSpace = 20) => {
        // CRITICAL FIX: More conservative space checking to prevent overlaps
        if (currentY + requiredSpace > maxContentHeight - 20) { // Extra buffer
          pageCount++;
          doc.addPage();

          // CONDITIONAL HEADER LOGIC: Adjust top margin based on header setting and content
          if (includeHeader && hasTestContent) {
            // Add pink header bar on new pages (no logo inside header)
            doc.setFillColor(236, 72, 153);
            doc.rect(0, 0, pageWidth, 8, 'F');

            yPosition = topMargin + 10; // Space after header (reduced since no title)
          } else {
            // No header: start content higher on page
            yPosition = topMargin + 5; // Reduced top margin for no-header layout
          }

          // CONDITIONAL COLUMN HEADERS: Re-add column headers on new page only if valid test data exists
          if (shouldDisplayHeaders) {
            yPosition = addColumnHeaders(doc, yPosition);
            console.log('Column headers re-added on new page - valid test data exists');
          } else {
            console.log('Column headers skipped on new page - no valid test data');
          }
          return yPosition;
        }
        return currentY;
      };

      // CONDITIONAL TEST RESULTS RENDERING: Only render if valid test data exists
      if (shouldDisplayHeaders && dynamicTests && dynamicTests.length > 0) {
        console.log('Rendering test results - valid test data detected');

        // FIXED: Render test results with proper spacing and typography
        dynamicTests.forEach((category, categoryIndex) => {
          yPosition = checkPageBreak(yPosition, 20);

          // FIXED: Category header with proper typography (13-14pt, ALL CAPS, bold)
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(13);
          doc.setTextColor(0, 0, 0);
          doc.text(category.category.toUpperCase(), 20, yPosition);
          yPosition += 8; // Adequate spacing after category

        category.tests.forEach((test) => {
          const estimatedTestHeight = calculateTestHeight(test);
          yPosition = checkPageBreak(yPosition, estimatedTestHeight);

          // FIXED: Test name with proper typography (11pt, bold)
          doc.setFont('helvetica', 'bold');
          doc.setFontSize(11);
          doc.text(test.name, 25, yPosition);
          yPosition += 6; // Consistent spacing

          // FIXED: Sub-tests with proper column alignment
          test.subTests.forEach((subTest) => {
            yPosition = checkPageBreak(yPosition, 6);

            // FIXED: Table data typography (11pt, normal)
            doc.setFont('helvetica', 'normal');
            doc.setFontSize(11);
            doc.setTextColor(0, 0, 0);

            // FIXED: Column alignment matching adjusted headers exactly
            const col1X = 15;  // Investigation/Method (moved left by 10)
            const col2X = 95;  // Result (moved left by 15)
            const col3X = 125; // Units (moved left by 15)
            const col4X = 150; // Reference Interval (moved left by 15)

            doc.text(subTest.name, col1X, yPosition);
            doc.text(subTest.result, col2X, yPosition);
            doc.text(subTest.unit, col3X, yPosition);

            if (!isComplexReferenceRange(subTest.reference)) {
              const simpleRef = subTest.reference || 'N/A';
              const truncatedRef = simpleRef.length > 15 ? simpleRef.substring(0, 12) + '...' : simpleRef;
              doc.text(truncatedRef, col4X, yPosition);
            } else {
              doc.text('See below', col4X, yPosition);
            }

            yPosition += 5; // Consistent line spacing
          });

          // CRITICAL FIX: Display complex reference ranges without overlap
          test.subTests.forEach((subTest) => {
            if (isComplexReferenceRange(subTest.reference)) {
              yPosition += 4; // Increased space before reference range

              // FIXED: Reference range header with adjusted positioning
              doc.setFontSize(10);
              doc.setFont('helvetica', 'bold');
              doc.setTextColor(0, 0, 0); // #808080 gray
              doc.text(`Reference Range (${subTest.name}):`, 20, yPosition); // Adjusted left position
              yPosition += 5; // Increased spacing

              // REFERENCE RANGE TEXT: Consistent 9pt font size regardless of header state
              doc.setFont('helvetica', 'normal');
              doc.setFontSize(9); // CONSISTENT 9pt font size for reference ranges
              doc.setTextColor(0, 0, 0);

              // STATE LOGGING: Debug reference range font size consistency
              console.log('Reference range text formatting:', {
                fontSize: 9,
                includeHeader: includeHeader,
                subTestName: subTest.name,
                referenceText: subTest.reference ? subTest.reference.substring(0, 50) + '...' : 'N/A'
              });

              const referenceLines = formatReferenceRangeForDisplay(subTest.reference);
              referenceLines.forEach((line) => {
                yPosition = checkPageBreak(yPosition, 5);
                // CRITICAL FIX: Proper text wrapping to prevent right-side overlap
                const wrappedLines = doc.splitTextToSize(line, 160); // Increased width with adjusted margins
                wrappedLines.forEach((wrappedLine) => {
                  doc.text(wrappedLine, 25, yPosition); // Adjusted left position
                  yPosition += 4; // Consistent line spacing
                });
              });

              // Reset to normal formatting
              doc.setFontSize(11);
              doc.setFont('helvetica', 'normal');
              doc.setTextColor(0, 0, 0);
              yPosition += 4; // Increased space after reference range
            }
          });

          // NOTES TEXT SIZE BUG FIX: Consistent font size regardless of header toggle
          if (test.notes && test.notes.trim()) {
            yPosition += 4; // Increased space before notes

            // FONT SIZE REDUCTION: Reduced to 8pt for more compact appearance
            doc.setFontSize(8); // REDUCED from 10pt to 8pt font size
            doc.setFont('helvetica', 'normal');
            doc.setTextColor(0, 0, 0);

            // STATE LOGGING: Debug font size consistency
            console.log('Notes text formatting:', {
              fontSize: 8, // Updated to reflect new size
              includeHeader: includeHeader,
              testName: test.name,
              notesLength: test.notes.length
            });

            // CRITICAL FIX: Proper text wrapping with adjusted positioning
            const noteLines = doc.splitTextToSize(`Notes: ${test.notes}`, 160); // Increased width with adjusted margins
            noteLines.forEach((line) => {
              yPosition = checkPageBreak(yPosition, 5);
              doc.text(line, 20, yPosition); // Adjusted left position
              yPosition += 3; // Reduced line spacing for 8pt font
            });

            // TYPOGRAPHY CONSISTENCY: Reset formatting to standard values
            doc.setFontSize(11); // Reset to standard table font size
            doc.setFont('helvetica', 'normal');
            doc.setTextColor(0, 0, 0);
            yPosition += 5; // Reduced space after notes for compact layout
          } else {
            yPosition += 3; // Reduced minimal space if no notes
          }
        });

        // FIXED: Category separator with proper spacing
        const isLastCategory = categoryIndex === dynamicTests.length - 1;
        if (!isLastCategory) {
          yPosition += 5; // Space before separator
          yPosition = checkPageBreak(yPosition, 8);

          // Optional subtle separator with uniform formatting
          doc.setDrawColor(0, 0, 0);
          doc.setLineWidth(1.2);
          doc.line(10, yPosition, pageWidth - 10, yPosition);
          yPosition += 8; // Space after separator
        } else {
          yPosition += 8; // Final spacing after last category
        }
        });

      } else {
        // NO VALID TEST DATA SCENARIO: Content flow directly to signature section
        console.log('No valid test data detected - skipping test results rendering');
        console.log('Test data validation failed:', {
          hasActualTestData: hasActualTestData,
          hasTransformedTestData: hasTransformedTestData,
          shouldDisplayHeaders: shouldDisplayHeaders
        });
        yPosition += 10; // Minimal spacing when no test data
      }

      // FIXED: END OF REPORT with increased spacing to prevent QR code overlap
      yPosition += 10; // Adequate space before end marker
      yPosition = checkPageBreak(yPosition, 35); // Increased space requirement for QR code

      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(0, 0, 0);
      doc.text('END OF REPORT', pageWidth / 2, yPosition, { align: 'center' });
      yPosition += 25; // CRITICAL FIX: Increased spacing to prevent QR code overlap (was 15, now 25)

      // FOOTER CONDITIONAL LOGIC: Only add footer when header is enabled
      actualPageCount = doc.internal.getNumberOfPages();
      if (includeHeader) {
        // Update all page footers with correct page numbers (only when header enabled)
        for (let i = 1; i <= actualPageCount; i++) {
          doc.setPage(i);
          addPersistentFooter(doc, pageWidth, pageHeight, i, actualPageCount);
        }
        console.log('Footer added to all pages (header mode enabled)');
      } else {
        console.log('Footer skipped (no-header mode enabled)');
      }

    } catch (err) {
      console.error('Error in generateTestResultsTable:', err);
      actualPageCount = doc.internal.getNumberOfPages();

      // FOOTER CONDITIONAL LOGIC: Apply same logic in error handling
      if (includeHeader) {
        const errorPageHeight = doc.internal.pageSize.getHeight();
        for (let i = 1; i <= actualPageCount; i++) {
          doc.setPage(i);
          addPersistentFooter(doc, pageWidth, errorPageHeight, i, actualPageCount);
        }
        console.log('Error handling: Footer added to all pages (header mode enabled)');
      } else {
        console.log('Error handling: Footer skipped (no-header mode enabled)');
      }
    }

    return { yPosition: yPosition, pageCount: actualPageCount };
  };

  // Supporting functions for enhanced PDF generation
  const transformReportDataForPDF = (reportData) => {
    try {
      console.log('Transforming billing report data for PDF:', reportData);

      const testItems = reportData.test_items || reportData.billing_items || [];

      if (!reportData || !testItems || testItems.length === 0) {
        console.warn('No test items found in report data, using fallback data');
        return getFallbackTestData();
      }

      console.log('Found test items:', testItems);
      const categories = {};

      testItems.forEach(item => {
        console.log('Processing test item:', item);

        const categoryName = item.department ||
                            item.test_master_data?.department ||
                            item.category ||
                            'GENERAL TESTS';

        if (!categories[categoryName]) {
          categories[categoryName] = {
            category: categoryName,
            tests: []
          };
        }

        const testEntry = {
          name: item.test_name || item.name || 'Unknown Test',
          notes: item.instructions ||
                item.test_master_data?.instructions ||
                item.interpretation ||
                item.test_master_data?.interpretation ||
                '',
          subTests: []
        };

        if (item.sub_tests && Array.isArray(item.sub_tests)) {
          item.sub_tests.forEach(subTest => {
            testEntry.subTests.push({
              name: subTest.name || subTest.test_name || subTest.parameter || 'Sub Test',
              result: subTest.result || subTest.value || subTest.test_result || 'Pending',
              unit: subTest.unit || subTest.units || subTest.measurement_unit || '',
              reference: subTest.reference_range || subTest.normal_range || subTest.reference || 'N/A'
            });
          });
        } else {
          testEntry.subTests.push({
            name: item.test_name || item.name || 'Test Result',
            result: 'Pending',
            unit: item.result_unit || item.test_master_data?.result_unit || '',
            reference: item.reference_range || item.test_master_data?.reference_range || 'N/A'
          });
        }

        categories[categoryName].tests.push(testEntry);
        console.log('Added test to category:', categoryName, testEntry);
      });

      const transformedData = Object.values(categories);
      console.log('Final transformed data for PDF:', transformedData);
      return transformedData.length > 0 ? transformedData : getFallbackTestData();

    } catch (error) {
      console.error('Error transforming billing report data:', error);
      return getFallbackTestData();
    }
  };

  const getFallbackTestData = () => {
    console.warn('Using fallback test data - no actual test results found in report');
    return [
      {
        category: 'GENERAL TESTS',
        tests: [
          {
            name: 'No Test Data Available',
            notes: 'This PDF was generated but no test results were found in the billing report.',
            subTests: [
              { name: 'Status', result: 'No Data', unit: '', reference: 'Check Report' }
            ]
          }
        ]
      }
    ];
  };

  const isComplexReferenceRange = (referenceText) => {
    if (!referenceText || referenceText.trim() === '') return false;

    const cleanText = referenceText.trim();
    const hasMultipleSegments = cleanText.includes(':') && cleanText.length > 30;
    const hasAgeGroups = /\b(month|year|adult|child|male|female|born)\b/i.test(cleanText);
    const isLong = cleanText.length > 40;

    return hasMultipleSegments || hasAgeGroups || isLong;
  };

  const formatReferenceRangeForDisplay = (referenceText) => {
    if (!referenceText || referenceText.trim() === '') return ['N/A'];

    let cleanText = referenceText.replace(/\n+/g, ' ').trim();
    const segments = [];

    if (cleanText.includes('month') || cleanText.includes('year') || cleanText.includes('Adult')) {
      const ageSegments = cleanText.split(/(?=\d+\s*(?:month|year)|Adult|Child|Male|Female|New Born|Cord Blood)/i);
      ageSegments.forEach(segment => {
        const trimmed = segment.trim();
        if (trimmed) segments.push(trimmed);
      });
    } else {
      const splitSegments = cleanText.split(/\s*[:|;]\s*(?=[A-Z]|\d)/)
        .filter(segment => segment.trim() !== '');
      segments.push(...splitSegments);
    }

    const formattedLines = [];
    segments.forEach(segment => {
      const trimmedSegment = segment.trim();
      if (trimmedSegment) {
        formattedLines.push(trimmedSegment);
      }
    });

    return formattedLines.length > 0 ? formattedLines : ['N/A'];
  };

  const calculateTestHeight = (test) => {
    let height = 5;

    test.subTests.forEach(subTest => {
      height += 4;

      if (isComplexReferenceRange(subTest.reference)) {
        const referenceLines = formatReferenceRangeForDisplay(subTest.reference);
        height += 2 + (referenceLines.length * 2.5) + 2;
      }
    });

    if (test.notes && test.notes.trim()) {
      const noteLines = test.notes.split('\n');
      height += 2 + (noteLines.length * 2.5) + 2;
    } else {
      height += 2;
    }

    return height;
  };

  // CRITICAL FIX: Column headers with proper alignment and spacing
  const addColumnHeaders = (doc, yPos) => {
    // FIXED: Table headers typography (11-12pt, bold)
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(11);
    doc.setTextColor(0, 0, 0);

    // FIXED: Move all columns left to prevent right-side overlap
    const col1X = 15;  // Investigation/Method (moved left by 10)
    const col2X = 95;  // Result (moved left by 15)
    const col3X = 125; // Units (moved left by 15)
    const col4X = 150; // Reference Interval (moved left by 15)

    doc.text('INVESTIGATION/METHOD', col1X, yPos);
    doc.text('RESULT', col2X, yPos);
    doc.text('UNITS', col3X, yPos);
    doc.text('BIOLOGICAL REF INTERVAL', col4X, yPos); // Now fits within margins

    // FIXED: Uniform underline for headers matching other lines
    doc.setDrawColor(0, 0, 0);
    doc.setLineWidth(1.2);
    doc.line(10, yPos + 2, doc.internal.pageSize.getWidth() - 10, yPos + 2);

    return yPos + 15; // Increased spacing after headers for better separation
  };

  // ENHANCED FOOTER: Branch information above 8pt pink footer bar
  const addPersistentFooter = (doc, currentPageWidth, currentPageHeight, currentPage = 1, totalPages = 1) => {
    // FOOTER CONTENT ENHANCEMENT: Branch locations text above pink footer bar
    const branchTextY = currentPageHeight - 16; // Position for branch text in white area

    // HEAD OFFICE POSITIONING: Above branches on left side
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(236, 72, 153); // Black text in white area
    doc.text('Head Office', 15, branchTextY - 5);

    // BRANCH TEXT CONTENT: Display branch locations list
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(0, 0, 0); // Black text in white area
    const branchText = 'Mayiladuthurai | Chidambaram | Sirkazhi | Sankanankovil | Kumbakonam | Pandanallur | Thirupanandal | Eravanchery | Nannilam | Thanjavur | Needamangalam | Thiruthuraipoondi | Tiruvarur | Avadi | Ambakkam';

    // BRANCH TEXT STYLING: Centered alignment with proper wrapping
    const wrappedBranchLines = doc.splitTextToSize(branchText, currentPageWidth - 30);
    wrappedBranchLines.forEach((line, index) => {
      doc.text(line, currentPageWidth / 2, branchTextY + (index * 4), { align: 'center' });
    });

    // SPACING: 5-10pt gap between branch text and pink footer bar
    const pinkFooterY = currentPageHeight - 8;
    doc.setFillColor(236, 72, 153); // Same pink color as header for consistency
    doc.rect(0, pinkFooterY, currentPageWidth, 8, 'F'); // 8pt height footer bar

    // FOOTER TEXT ENHANCEMENT: Head Office text on left side of pink bar
    doc.setFontSize(8);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(255, 255, 255); // White text on pink background
    // doc.text('Head Office', 15, pinkFooterY + 5); // Positioned for 8pt footer height

    // FOOTER CONTENT: Contact information in center (adjusted for 8pt height)
    doc.setFontSize(10);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(255, 255, 255); // White text on pink background
    const contactInfo = 'Customer Care No: 1800 572 4455';
    doc.text(contactInfo, currentPageWidth / 2, pinkFooterY + 5, { align: 'center' });

    // FOOTER PAGINATION: Page numbering on right side (adjusted for 8pt height)
    doc.setFontSize(7);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(255, 255, 255); // White text on pink background
    doc.text(`Page ${currentPage} of ${totalPages}`, currentPageWidth - 15, pinkFooterY + 5, { align: 'right' });

    console.log('Enhanced footer with branches added:', {
      footerHeight: '8pt',
      branchTextPosition: branchTextY,
      pinkFooterPosition: pinkFooterY,
      branchTextLines: wrappedBranchLines.length,
      colorConsistency: 'Matches header pink (236, 72, 153)'
    });
  };

  // Load signature image function
  const loadSignatureImage = () => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        resolve(canvas.toDataURL('image/jpeg'));
      };
      img.onerror = () => {
        console.warn('Signature image not found, using text-based signature');
        resolve(null);
      };
      img.src = '/signature.jpeg';
    });
  };

  // FIXED: Signature section with proper spacing and positioning
  const generateQRCodeAndSignatureSection = async (doc, qrCodeImg, pageWidth, pageHeight, contentEndY = 0) => {
    try {
      // Load signature image
      const signatureImg = await loadSignatureImage();

      // FOOTER HEIGHT ADJUSTMENT: Updated bottom margin for enhanced footer with branch text
      const bottomMargin = 35; // Space for 8pt footer + branch text above (was 20 for simple footer)
      const availableSpace = pageHeight - contentEndY - bottomMargin;
      const signatureSectionHeight = 40;

      // FIXED: Better positioning logic
      const signatureY = availableSpace >= signatureSectionHeight
        ? contentEndY + 15 // Adequate space after content
        : pageHeight - bottomMargin - signatureSectionHeight;

      // FIXED: Left signature with proper margins
      const leftMargin = 20;
      const rightMargin = pageWidth - 20;

      doc.setTextColor(0, 0, 0);
      doc.setDrawColor(0, 0, 0);
      doc.setLineWidth(1.2);
      doc.line(leftMargin, signatureY + 5, leftMargin + 60, signatureY + 5);

      doc.setFont('helvetica', 'bold');
      doc.setFontSize(10);
      doc.text('Verified By', leftMargin + 30, signatureY + 15, { align: 'center' });

      const verifierName = user?.role === 'lab_technician' || user?.role === 'technician'
        ? `${user.first_name || ''} ${user.last_name || ''}`.trim()
        : user?.first_name && user?.last_name
          ? `${user.first_name} ${user.last_name}`
          : 'Lab Technician';

      const verifierRole = user?.role === 'lab_technician' ? 'Lab Technician'
        : user?.role === 'technician' ? 'Medical Technician'
        : user?.role === 'doctor' ? 'Medical Officer'
        : 'Lab Technician';

      doc.text(verifierName, leftMargin + 30, signatureY + 22, { align: 'center' });
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(9);
      doc.text(verifierRole, leftMargin + 30, signatureY + 28, { align: 'center' });

      // FIXED: QR Code positioning in center
      if (qrCodeImg) {
        const qrSize = 25;
        const qrX = (pageWidth / 2) - (qrSize / 2);
        const qrY = signatureY - 5;

        doc.setFillColor(255, 255, 255);
        doc.rect(qrX - 1, qrY - 1, qrSize + 2, qrSize + 2, 'F');
        doc.addImage(qrCodeImg, 'PNG', qrX, qrY, qrSize, qrSize);
      }

      // ENHANCED RIGHT SIGNATURE: With text-based handwritten-style signature
      doc.setDrawColor(0, 0, 0);
      doc.setLineWidth(1.2);
      doc.line(rightMargin - 60, signatureY + 5, rightMargin, signatureY + 5);

      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(0, 0, 0);
      doc.text('Authorized By', rightMargin - 30, signatureY + 15, { align: 'center' });

      // TEXT-BASED SIGNATURE: Handwritten-style text signature
      const authorizerName = user?.role === 'admin' || user?.role === 'manager'
        ? `${user.first_name || ''} ${user.last_name || ''}`.trim()
        : currentTenantContext?.manager_name || 'Dr. S.Asokkumar, PhD.';

      const authorizerTitle = user?.role === 'admin' ? 'Laboratory Administrator'
        : user?.role === 'manager' ? 'Laboratory Manager'
        : user?.role === 'doctor' ? 'Medical Director'
        : currentTenantContext?.manager_title || 'Clinical Microbiologist & QM';

      // DOCTOR NAME: First display the doctor name
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      doc.text(authorizerName, rightMargin - 30, signatureY + 22, { align: 'center' });

      // SIGNATURE: Position signature precisely under the doctor name
      const signatureTextY = signatureY + 28; // Positioned under doctor name

      if (signatureImg) {
        // Add actual signature image
        const sigWidth = 40;
        const sigHeight = 15;
        const sigX = rightMargin - 30 - (sigWidth / 2);
        doc.addImage(signatureImg, 'JPEG', sigX, signatureTextY - 5, sigWidth, sigHeight);
      } else {
        // Fallback to text-based signature
        doc.setFont('helvetica', 'italic');
        doc.setFontSize(12);
        doc.setTextColor(0, 0, 0);
        doc.text('_________________', rightMargin - 30, signatureTextY, { align: 'center' });
      }

      console.log('Professional signature positioning:', {
        doctorName: authorizerName,
        doctorNameY: signatureY + 22,
        signatureY: signatureTextY,
        referenceLineY: signatureY + 5,
        positioning: 'signature under doctor name, above reference line'
      });

      // TITLE: Below signature
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(9);
      doc.text(authorizerTitle, rightMargin - 30, signatureTextY + 6, { align: 'center' });

    } catch (err) {
      console.error('Error in generateQRCodeAndSignatureSection:', err);
    }
  };

  const clearSearch = () => {
    setSearchParams({
      sid: '',
      patient_name: '',
      mobile: '',
      date_from: '',
      date_to: ''
    });
    loadReports();
  };

  // Pagination calculations
  const totalItems = reports.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedReports = reports.slice(startIndex, endIndex);

  const handlePageChange = (page) => {
    setCurrentPage(page);
    // Scroll to top of table on page change
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="billing-reports-container">
      {/* Header */}
      <div className="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 className="h3 mb-0 text-gray-800">
          <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
          Billing Reports
        </h1>
        <div className="d-flex gap-2">
          <Button 
            variant="outline-primary" 
            onClick={loadReports}
            disabled={loading}
          >
            <FontAwesomeIcon icon={faSpinner} spin={loading} className="me-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row className="mb-4">
          {/* Debug: Log stats object */}
          {console.log('[BillingReports] Rendering stats:', stats)}
          <Col md={3}>
            <Card className="border-left-primary shadow h-100 py-2">
              <Card.Body>
                <Row className="no-gutters align-items-center">
                  <Col className="mr-2">
                    <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                      Total Reports
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      {stats.total_reports || 0}
                    </div>
                  </Col>
                  <Col xs="auto">
                    <FontAwesomeIcon icon={faFileInvoiceDollar} className="fa-2x text-gray-300" />
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="border-left-success shadow h-100 py-2">
              <Card.Body>
                <Row className="no-gutters align-items-center">
                  <Col className="mr-2">
                    <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                      Total Amount
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      {billingReportsAPI.formatCurrency(stats.total_amount || 0)}
                    </div>
                  </Col>
                  <Col xs="auto">
                    <FontAwesomeIcon icon={faCheckCircle} className="fa-2x text-gray-300" />
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="border-left-info shadow h-100 py-2">
              <Card.Body>
                <Row className="no-gutters align-items-center">
                  <Col className="mr-2">
                    <div className="text-xs font-weight-bold text-info text-uppercase mb-1">
                      Recent Reports (7 days)
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      {stats.recent_reports_count || 0}
                    </div>
                  </Col>
                  <Col xs="auto">
                    <FontAwesomeIcon icon={faCalendarAlt} className="fa-2x text-gray-300" />
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="border-left-warning shadow h-100 py-2">
              <Card.Body>
                <Row className="no-gutters align-items-center">
                  <Col className="mr-2">
                    <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                      Access Level
                    </div>
                    <div className="h6 mb-0 font-weight-bold text-gray-800">
                      {stats.user_access_level === 'all_franchises' ? 'All Franchises' : 'Own Franchise'}
                    </div>
                  </Col>
                  <Col xs="auto">
                    <FontAwesomeIcon icon={faBuilding} className="fa-2x text-gray-300" />
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Search Section */}
      <Card className="shadow mb-4">
        <Card.Header className="py-3">
          <div className="d-flex justify-content-between align-items-center">
            <h6 className="m-0 font-weight-bold text-primary">Search Billing Reports</h6>
            {canAccessAllFranchises() && (
              <Dropdown>
                <Dropdown.Toggle variant="outline-primary" size="sm">
                  <FontAwesomeIcon icon={faFilter} className="me-2" />
                  {selectedFranchiseId
                    ? getAvailableFranchises().find(f => f.id === selectedFranchiseId)?.name || 'Select Franchise'
                    : 'All Franchises'
                  }
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item
                    onClick={() => setSelectedFranchiseId(null)}
                    className={!selectedFranchiseId ? 'active' : ''}
                  >
                    <FontAwesomeIcon icon={faBuilding} className="me-2" />
                    All Franchises
                  </Dropdown.Item>
                  <Dropdown.Divider />
                  {getAvailableFranchises().map((franchise) => (
                    <Dropdown.Item
                      key={franchise.id}
                      onClick={() => setSelectedFranchiseId(franchise.id)}
                      className={selectedFranchiseId === franchise.id ? 'active' : ''}
                    >
                      <FontAwesomeIcon icon={faBuilding} className="me-2" />
                      {franchise.name}
                      <small className="text-muted d-block">{franchise.site_code}</small>
                    </Dropdown.Item>
                  ))}
                </Dropdown.Menu>
              </Dropdown>
            )}
          </div>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3 position-relative">
                <Form.Label>SID Number</Form.Label>
                <InputGroup>
                  <Form.Control
                    type="text"
                    placeholder="Enter SID number (e.g., MYD001, SKZ001, TNJ001)"
                    value={searchParams.sid}
                    onChange={(e) => handleSIDChange(e.target.value)}
                    onFocus={() => setShowSidSuggestions(sidSuggestions.length > 0)}
                  />
                  <Button variant="outline-secondary" onClick={handleSearch} disabled={searchLoading}>
                    <FontAwesomeIcon icon={faSearch} />
                  </Button>
                </InputGroup>
                
                {/* SID Autocomplete Dropdown */}
                {showSidSuggestions && sidSuggestions.length > 0 && (
                  <div className="sid-suggestions-dropdown">
                    {sidSuggestions.map((sid, index) => (
                      <div
                        key={index}
                        className="sid-suggestion-item"
                        onClick={() => handleSIDSelect(sid)}
                      >
                        {sid}
                      </div>
                    ))}
                  </div>
                )}
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Patient Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter patient name"
                  value={searchParams.patient_name}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, patient_name: e.target.value }))}
                />
              </Form.Group>
            </Col>
          </Row>
          
          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Mobile Number</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter mobile number"
                  value={searchParams.mobile}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, mobile: e.target.value }))}
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>From Date</Form.Label>
                <Form.Control
                  type="date"
                  value={searchParams.date_from}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, date_from: e.target.value }))}
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>To Date</Form.Label>
                <Form.Control
                  type="date"
                  value={searchParams.date_to}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, date_to: e.target.value }))}
                />
              </Form.Group>
            </Col>
          </Row>
          
          {/* HEADER TOGGLE OPTION: PDF Generation Settings */}
          <Row className="mb-3">
            <Col md={12}>
              <Card className="border-info">
                <Card.Body className="py-2">
                  <div className="d-flex align-items-center justify-content-between">
                    <div className="d-flex align-items-center">
                      <FontAwesomeIcon icon={faDownload} className="text-info me-2" />
                      <span className="fw-bold text-info">PDF Generation Options:</span>
                    </div>
                    <Form.Check
                      type="switch"
                      id="include-header-switch"
                      label="Include Header in PDF"
                      checked={includeHeader}
                      onChange={(e) => setIncludeHeader(e.target.checked)}
                      className="mb-0"
                    />
                  </div>
                  <small className="text-muted">
                    {includeHeader
                      ? "PDF will include pink header bar with logo on all pages"
                      : "PDF will be generated without header bar for cleaner layout"
                    }
                  </small>
                </Card.Body>
              </Card>
            </Col>
          </Row>

          <div className="d-flex gap-2">
            <Button
              variant="primary"
              onClick={handleSearch}
              disabled={searchLoading}
            >
              <FontAwesomeIcon icon={searchLoading ? faSpinner : faSearch} spin={searchLoading} className="me-2" />
              Search Reports
            </Button>
            <Button variant="outline-secondary" onClick={clearSearch}>
              Clear
            </Button>
          </div>
        </Card.Body>
      </Card>

      {/* Enhanced PDF Debug Section */}
      <Card className="shadow mb-4">
        <Card.Header className="py-3">
          <h6 className="m-0 font-weight-bold text-success">Enhanced PDF Generation Status</h6>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={8}>
              <div className="d-flex align-items-center mb-2">
                <FontAwesomeIcon
                  icon={jsPDF ? faCheckCircle : faExclamationTriangle}
                  className={`me-2 ${jsPDF ? 'text-success' : 'text-danger'}`}
                />
                <span className={jsPDF ? 'text-success' : 'text-danger'}>
                  jsPDF Library: {jsPDF ? 'Available' : 'Not Available'}
                </span>
              </div>
              <div className="d-flex align-items-center mb-2">
                <FontAwesomeIcon
                  icon={QRCode ? faCheckCircle : faExclamationTriangle}
                  className={`me-2 ${QRCode ? 'text-success' : 'text-warning'}`}
                />
                <span className={QRCode ? 'text-success' : 'text-warning'}>
                  QRCode Library: {QRCode ? 'Available' : 'Not Available (QR codes disabled)'}
                </span>
              </div>
              <div className="d-flex align-items-center mb-2">
                <FontAwesomeIcon
                  icon={JsBarcode ? faCheckCircle : faExclamationTriangle}
                  className={`me-2 ${JsBarcode ? 'text-success' : 'text-warning'}`}
                />
                <span className={JsBarcode ? 'text-success' : 'text-warning'}>
                  JsBarcode Library: {JsBarcode ? 'Available' : 'Not Available (Barcodes disabled)'}
                </span>
              </div>
              <small className="text-muted">
                Enhanced PDF features: Compact spacing, dynamic signatures, separate reference ranges, accurate page numbering
              </small>
            </Col>
            <Col md={4}>
              <Button
                variant="outline-info"
                size="sm"
                onClick={() => {
                  console.log('=== ENHANCED PDF LIBRARIES TEST ===');
                  console.log('jsPDF:', !!jsPDF, typeof jsPDF);
                  console.log('QRCode:', !!QRCode, typeof QRCode);
                  console.log('JsBarcode:', !!JsBarcode, typeof JsBarcode);
                  console.log('autoTable:', autoTableAvailable);
                  console.log('Current User:', user);
                  console.log('Current Tenant:', currentTenantContext);
                  console.log('=== END TEST ===');
                  alert('Enhanced PDF libraries test completed. Check console for details.');
                }}
                className="w-100"
              >
                Test Enhanced PDF Libraries
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Reports Table */}
      <Card className="shadow">
        <Card.Header className="py-3">
          <div className="d-flex justify-content-between align-items-center">
            <h6 className="m-0 font-weight-bold text-primary">
              Billing Reports ({reports.length})
            </h6>
            {reports.length > 0 && (
              <small className="text-muted">
                Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} reports
              </small>
            )}
          </div>
        </Card.Header>
        <Card.Body>
          {loading ? (
            <div className="text-center py-4">
              <Spinner animation="border" variant="primary" />
              <p className="mt-2">Loading billing reports...</p>
            </div>
          ) : reports.length === 0 ? (
            <div className="text-center py-4">
              <FontAwesomeIcon icon={faInfoCircle} size="3x" className="text-muted mb-3" />
              <h5 className="text-muted">No billing reports found</h5>
              <p className="text-muted">Try adjusting your search criteria or create a new billing record.</p>
            </div>
          ) : (
            <>
              <div className="table-responsive">
                <Table hover>
                  <thead>
                    <tr>
                      <th>SID Number</th>
                      <th>Patient</th>
                      <th>Clinic</th>
                      <th>Billing Date</th>
                      <th>Tests</th>
                      <th>Amount</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Array.isArray(paginatedReports) ? paginatedReports.map((report) => (
                    <tr key={report.id}>
                      <td>
                        <strong className="text-primary">{report.sid_number}</strong>
                      </td>
                      <td>
                        <div>
                          <FontAwesomeIcon icon={faUser} className="me-1 text-muted" />
                          {report.patient_name}
                        </div>
                        {report.patient_mobile && (
                          <small className="text-muted">
                            <FontAwesomeIcon icon={faPhone} className="me-1" />
                            {report.patient_mobile}
                          </small>
                        )}
                      </td>
                      <td>
                        <FontAwesomeIcon icon={faBuilding} className="me-1 text-muted" />
                        {report.clinic_name}
                      </td>
                      <td>
                        <FontAwesomeIcon icon={faCalendarAlt} className="me-1 text-muted" />
                        {billingReportsAPI.formatDate(report.billing_date)}
                      </td>
                      <td>
                        <Badge bg="info">{report.test_count} tests</Badge>
                      </td>
                      <td>
                        <strong>{billingReportsAPI.formatCurrency(report.total_amount)}</strong>
                      </td>
                      <td>
                        <Badge bg={billingReportsAPI.getStatusVariant(report.status)}>
                          {report.status}
                        </Badge>
                      </td>
                      <td>
                        <div className="d-flex gap-1">
                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip>View Report Details</Tooltip>}
                          >
                            <Button
                              variant="outline-primary"
                              size="sm"
                              onClick={() => handleViewReport(report)}
                            >
                              <FontAwesomeIcon icon={faEye} />
                            </Button>
                          </OverlayTrigger>

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip>Download PDF Report</Tooltip>}
                          >
                            <Button
                              variant="outline-success"
                              size="sm"
                              onClick={() => handleDownloadPDF(report)}
                              className="ms-1"
                              disabled={downloadingPDF}
                            >
                              {downloadingPDF ? (
                                <FontAwesomeIcon icon={faSpinner} spin />
                              ) : (
                                <FontAwesomeIcon icon={faDownload} />
                              )}
                            </Button>
                          </OverlayTrigger>

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip>Download PDF</Tooltip>}
                          >
                            <Button
                              variant="outline-success"
                              size="sm"
                              onClick={() => handleDownloadPDF(report)}
                            >
                              <FontAwesomeIcon icon={faDownload} />
                            </Button>
                          </OverlayTrigger>
                        </div>
                      </td>
                    </tr>
                  )) : (
                    <tr>
                      <td colSpan="8" className="text-center text-danger">
                        Error: Reports data is not an array (type: {typeof paginatedReports})
                      </td>
                    </tr>
                  )}
                </tbody>
              </Table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-4">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalItems}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                  showInfo={false}
                  size="sm"
                />
              </div>
            )}
          </>
          )}
        </Card.Body>
      </Card>

      {/* Report Details Modal */}
      <Modal show={showReportModal} onHide={() => setShowReportModal(false)} size="xl" scrollable>
        <Modal.Header closeButton>
          <Modal.Title>
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
            Billing Report Details
            {selectedReport && (
              <Badge bg="primary" className="ms-2">{selectedReport.sid_number}</Badge>
            )}
          </Modal.Title>
          {selectedReport && (
            <Button
              variant="success"
              size="sm"
              onClick={() => handleDownloadPDF(selectedReport)}
              className="ms-auto"
              disabled={downloadingPDF}
            >
              {downloadingPDF ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} spin className="me-1" />
                  Downloading...
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faDownload} className="me-1" />
                  Download PDF
                </>
              )}
            </Button>
          )}
        </Modal.Header>
        <Modal.Body>
          {reportModalLoading ? (
            <div className="text-center py-4">
              <Spinner animation="border" variant="primary" />
              <p className="mt-2">Loading report details...</p>
            </div>
          ) : selectedReport ? (
            <div>
              {/* Enhanced PDF Options Section */}
              <Card className="mb-4">
                <Card.Body>
                  <h6 className="text-primary mb-3">
                    <FontAwesomeIcon icon={faDownload} className="me-2" />
                    Enhanced PDF Download Options
                  </h6>
                  <Row>
                    <Col md={6}>
                      <Form.Check
                        type="checkbox"
                        id="include-header-checkbox-modal"
                        label="Include Header in PDF"
                        checked={includeHeader}
                        onChange={(e) => setIncludeHeader(e.target.checked)}
                        className="mb-2"
                      />
                      <small className="text-muted">
                        Enhanced PDF with optimized spacing, dynamic signatures, and professional formatting.
                      </small>
                    </Col>
                    <Col md={6}>
                      <div className="d-flex align-items-center mb-3">
                        <FontAwesomeIcon icon={faInfoCircle} className="text-info me-2" />
                        <small className="text-muted">
                          Enhanced PDF includes QR codes, dynamic user signatures, and optimized medical report layout.
                        </small>
                      </div>
                      <Button
                        variant="outline-info"
                        size="sm"
                        onClick={() => {
                          console.log('=== ENHANCED PDF DEBUG INFO - BILLING REPORTS ===');
                          console.log('Current User:', user);
                          console.log('Current Tenant:', currentTenantContext);
                          console.log('Selected Report:', selectedReport);
                          console.log('Report Keys:', Object.keys(selectedReport || {}));
                          console.log('Patient Info:', selectedReport?.patient_info);
                          console.log('Test Items:', selectedReport?.test_items);
                          console.log('Clinic Info:', selectedReport?.clinic_info);
                          console.log('Financial Summary:', selectedReport?.financial_summary);
                          console.log('Enhanced PDF Libraries Status:');
                          console.log('- jsPDF available:', !!jsPDF);
                          console.log('- QRCode available:', !!QRCode);
                          console.log('- JsBarcode available:', !!JsBarcode);
                          console.log('- autoTable available:', autoTableAvailable);
                          console.log('User Access Level:', user?.role);
                          console.log('User Tenant ID:', user?.tenant_id);
                          console.log('Report Tenant ID:', selectedReport?.tenant_id);
                          console.log('Enhanced Features Status:');
                          console.log('- Compact spacing: ENABLED');
                          console.log('- Separate reference ranges: ENABLED');
                          console.log('- Dynamic signatures: ENABLED');
                          console.log('- Fixed page numbering: ENABLED');

                          // Test data transformation
                          if (selectedReport) {
                            const transformedData = transformReportDataForPDF(selectedReport);
                            console.log('Transformed Test Data:', transformedData);
                          }

                          console.log('=== END ENHANCED PDF DEBUG INFO ===');
                          alert('Check browser console for detailed enhanced PDF debug information');
                        }}
                        className="me-2"
                      >
                        Debug Enhanced PDF
                      </Button>
                      <Button
                        variant="outline-secondary"
                        size="sm"
                        onClick={() => {
                          console.log('Testing Enhanced PDF libraries...');
                          console.log('jsPDF available:', !!jsPDF);
                          console.log('QRCode available:', !!QRCode);
                          console.log('JsBarcode available:', !!JsBarcode);
                          console.log('autoTable available:', autoTableAvailable);
                          console.log('PDF Libraries Import Status:');
                          console.log('- jsPDF type:', typeof jsPDF);
                          console.log('- QRCode type:', typeof QRCode);
                          console.log('- JsBarcode type:', typeof JsBarcode);
                          alert('Check browser console for enhanced PDF library status');
                        }}
                      >
                        Test Libraries
                      </Button>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
              {/* Report Header */}
              <Row className="mb-4">
                <Col md={6}>
                  <Card className="border-left-primary h-100">
                    <Card.Body>
                      <h6 className="text-primary mb-2">Report Information</h6>
                      <p className="mb-1"><strong>SID Number:</strong> {selectedReport.sid_number}</p>
                      <p className="mb-1"><strong>Billing Date:</strong> {billingReportsAPI.formatDate(selectedReport.billing_date)}</p>
                      <p className="mb-1"><strong>Generated:</strong> {billingReportsAPI.formatDateTime(selectedReport.generation_timestamp)}</p>
                      <p className="mb-0"><strong>Status:</strong> <Badge bg={billingReportsAPI.getStatusVariant(selectedReport.metadata?.status)}>{selectedReport.metadata?.status}</Badge></p>
                    </Card.Body>
                  </Card>
                </Col>
                <Col md={6}>
                  <Card className="border-left-info h-100">
                    <Card.Body>
                      <h6 className="text-info mb-2">Clinic Information</h6>
                      <p className="mb-1"><strong>Clinic:</strong> {selectedReport.clinic_info?.name}</p>
                      <p className="mb-1"><strong>Site Code:</strong> {selectedReport.clinic_info?.site_code}</p>
                      <p className="mb-1"><strong>Contact:</strong> {selectedReport.clinic_info?.contact_phone}</p>
                      <p className="mb-0"><strong>Email:</strong> {selectedReport.clinic_info?.email}</p>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>

              {/* Patient Information */}
              <h6 className="text-primary mb-3">
                <FontAwesomeIcon icon={faUser} className="me-2" />
                Patient Information
              </h6>
              <Row className="mb-3">
                <Col md={6}>
                  <p className="mb-1"><strong>Name:</strong> {selectedReport.patient_info?.full_name}</p>
                  <p className="mb-1"><strong>Patient ID:</strong> {selectedReport.patient_info?.patient_id}</p>
                  <p className="mb-1"><strong>Date of Birth:</strong> {billingReportsAPI.formatDate(selectedReport.patient_info?.date_of_birth)}</p>
                </Col>
                <Col md={6}>
                  <p className="mb-1"><strong>Age/Gender:</strong> {selectedReport.patient_info?.age} / {selectedReport.patient_info?.gender}</p>
                  <p className="mb-1"><strong>Blood Group:</strong> {selectedReport.patient_info?.blood_group || 'N/A'}</p>
                  <p className="mb-1"><strong>Mobile:</strong> {selectedReport.patient_info?.mobile}</p>
                </Col>
              </Row>
              {selectedReport.patient_info?.email && (
                <Row className="mb-3">
                  <Col md={12}>
                    <p className="mb-1"><strong>Email:</strong> {selectedReport.patient_info?.email}</p>
                  </Col>
                </Row>
              )}

              {/* Billing Header */}
              {selectedReport.billing_header && (
                <>
                  <h6 className="text-primary mb-3">
                    <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
                    Billing Information
                  </h6>
                  <Row className="mb-3">
                    <Col md={6}>
                      <p className="mb-1"><strong>Invoice Number:</strong> {selectedReport.billing_header.invoice_number}</p>
                      <p className="mb-1"><strong>Referring Doctor:</strong> {selectedReport.billing_header.referring_doctor}</p>
                    </Col>
                    <Col md={6}>
                      <p className="mb-1"><strong>Payment Status:</strong> {selectedReport.billing_header.payment_status}</p>
                      <p className="mb-1"><strong>Payment Method:</strong> {selectedReport.billing_header.payment_method}</p>
                    </Col>
                  </Row>
                </>
              )}

              {/* Test Items - Enhanced Card Layout */}
              <PaginatedTestCards
                testItems={selectedReport.test_items || []}
                title="Test Details"
                itemsPerPage={5}
              />

              {/* Unmatched Tests Warning */}
              {selectedReport.unmatched_tests && selectedReport.unmatched_tests.length > 0 && (
                <Alert variant="warning" className="mb-3">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
                  <strong>Unmatched Tests ({selectedReport.unmatched_tests.length}):</strong>
                  <ul className="mb-0 mt-2">
                    {selectedReport.unmatched_tests.map((test, index) => (
                      <li key={index}>{test}</li>
                    ))}
                  </ul>
                </Alert>
              )}

              {/* Financial Summary */}
              <h6 className="text-primary mb-3">
                <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
                Financial Summary
              </h6>
              {selectedReport.financial_summary && (
                <Row>
                  <Col md={6}>
                    <p className="mb-1"><strong>Bill Amount:</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.bill_amount)}</p>
                    <p className="mb-1"><strong>Other Charges:</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.other_charges)}</p>
                    <p className="mb-1"><strong>Discount ({selectedReport.financial_summary.discount_percent}%):</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.discount_amount)}</p>
                    <p className="mb-1"><strong>Subtotal:</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.subtotal)}</p>
                  </Col>
                  <Col md={6}>
                    <p className="mb-1"><strong>GST ({selectedReport.financial_summary.gst_rate}%):</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.gst_amount)}</p>
                    <p className="mb-1"><strong>Total Amount:</strong> <span className="text-success fw-bold">{billingReportsAPI.formatCurrency(selectedReport.financial_summary.total_amount)}</span></p>
                    <p className="mb-1"><strong>Paid Amount:</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.paid_amount)}</p>
                    <p className="mb-0"><strong>Balance:</strong> <span className={selectedReport.financial_summary.balance > 0 ? 'text-danger fw-bold' : 'text-success'}>{billingReportsAPI.formatCurrency(selectedReport.financial_summary.balance)}</span></p>
                  </Col>
                </Row>
              )}

              {/* Report Metadata */}
              {selectedReport.metadata && (
                <>
                  <h6 className="text-primary mb-3 mt-4">Report Metadata</h6>
                  <Row>
                    <Col md={6}>
                      <p className="mb-1"><strong>Test Match Rate:</strong>
                        <span className={billingReportsAPI.getMatchRateColor(selectedReport.metadata.test_match_success_rate)}>
                          {' '}{Math.round(selectedReport.metadata.test_match_success_rate * 100)}%
                        </span>
                      </p>
                      <p className="mb-1"><strong>Total Tests:</strong> {selectedReport.metadata.total_tests}</p>
                    </Col>
                    <Col md={6}>
                      <p className="mb-1"><strong>Matched Tests:</strong> {selectedReport.metadata.matched_tests_count}</p>
                      <p className="mb-1"><strong>Unmatched Tests:</strong> {selectedReport.metadata.unmatched_tests_count}</p>
                    </Col>
                  </Row>
                </>
              )}
            </div>
          ) : (
            <Alert variant="warning">No report details available</Alert>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowReportModal(false)}>
            Close
          </Button>
          {selectedReport && (
            <Button
              variant="primary"
              onClick={() => handleDownloadPDF(selectedReport)}
            >
              <FontAwesomeIcon icon={faDownload} className="me-2" />
              Download PDF
            </Button>
          )}
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default BillingReports;
